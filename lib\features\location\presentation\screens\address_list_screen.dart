import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_button.dart';
import '../../services/adress_services.dart';

class AddressListScreen extends StatefulWidget {
  final bool selectMode;
  final bool allowEdit;
  final Function(AddressModel)? onAddressSelected;

  const AddressListScreen({
    super.key,
    this.selectMode = false,
    this.allowEdit = false,
    this.onAddressSelected,
  });

  @override
  State<AddressListScreen> createState() => _AddressListScreenState();
}

class _AddressListScreenState extends State<AddressListScreen> {
  final AddressService _addressService = AddressService();
  List<AddressModel> _addresses = [];
  bool _isLoading = true;
  String? _defaultAddressId;

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  Future<void> _loadAddresses() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final addresses =
          await _addressService.getAllAddresses(); // Fast (cached)
      final selectedAddress =
          _addressService.getCurrentSelectedAddressSync(); // Instant (no GPS)

      setState(() {
        _addresses = addresses;
        _defaultAddressId = selectedAddress?.id;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Failed to load addresses');
    }
  }

  Future<void> _deleteAddress(String addressId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _addressService.deleteAddress(addressId);
      // No need to reload - deleteAddress already updates local state
      setState(() {
        _addresses.removeWhere((address) => address.id == addressId);
        _isLoading = false;
      });
      _showSuccessSnackBar('Address deleted');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Failed to delete address');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _navigateToAddAddress() {
    context
        .push(
      RouteNames.mapForNewAddress,
    )
        .then((_) {
      _loadAddresses();
    });
  }

  void _navigateToEditAddress(AddressModel address) {
    context
        .push(
      RouteNames.mapForEditAddress,
      extra: address,
    )
        .then((_) {
      _loadAddresses();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.selectMode ? 'Select Address' : 'My Addresses'),
        backgroundColor: AppColors.primaryAverage,
        foregroundColor: Colors.white,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primaryAverage,
          statusBarIconBrightness: Brightness.light,
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _addresses.isEmpty
              ? _buildEmptyState()
              : _buildAddressList(),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddAddress,
        backgroundColor: AppColors.primaryAverage,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No addresses found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add a new address to get started',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          AppButton(
            text: 'Add New Address',
            onPressed: _navigateToAddAddress,
            prefixIcon: const Icon(Icons.add_location_alt,
                color: Colors.white, size: 18),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _addresses.length,
      itemBuilder: (context, index) {
        final address = _addresses[index];
        final isDefault = address.id == _defaultAddressId;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: isDefault
                ? const BorderSide(color: AppColors.primaryAverage, width: 2)
                : BorderSide.none,
          ),
          child: InkWell(
            onTap: widget.selectMode
                ? () {
                    // Update AddressService with manually selected address
                    _addressService.setSelectedAddress(address);
                    if (widget.onAddressSelected != null) {
                      widget.onAddressSelected!(address);
                    }
                  }
                : null,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      _buildAddressTypeTag(address.addressType ?? ''),
                      const SizedBox(width: 8),
                      if (isDefault)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primaryAverage.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'SELECTED',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primaryAverage,
                            ),
                          ),
                        ),
                      const Spacer(),
                      if (!widget.selectMode || widget.allowEdit) ...[
                        IconButton(
                          onPressed: () => _navigateToEditAddress(address),
                          icon: const Icon(
                            Icons.edit,
                            size: 18,
                            color: Colors.grey,
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 36,
                            minHeight: 36,
                          ),
                          padding: EdgeInsets.zero,
                        ),
                        IconButton(
                          onPressed: () => _deleteAddress(address.id ?? ''),
                          icon: const Icon(
                            Icons.delete,
                            size: 18,
                            color: Colors.red,
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 36,
                            minHeight: 36,
                          ),
                          padding: EdgeInsets.zero,
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    address.fullAddress ?? '',
                    style: const TextStyle(
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                  if (address.landmark != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Landmark: ${address.landmark}',
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                  const SizedBox(height: 12),

                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAddressTypeTag(String addressType) {
    IconData icon;
    Color color;

    switch (addressType.toLowerCase()) {
      case 'home':
        icon = Icons.home;
        color = Colors.green;
        break;
      case 'work':
        icon = Icons.work;
        color = Colors.blue;
        break;
      default:
        icon = Icons.place;
        color = Colors.orange;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            addressType.toUpperCase(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
