import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import 'package:rozana/routes/app_router.dart';
import 'package:rozana/core/utils/text_field_manager.dart';
import 'package:rozana/widgets/custom_textfield.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/app_validators.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../widgets/custom_button.dart';

class AddressFormScreen extends StatefulWidget {
  final AddressModel? address;
  final bool fromCart;
  final double? latitude;
  final double? longitude;

  const AddressFormScreen({
    super.key,
    this.address,
    this.fromCart = false,
    this.latitude,
    this.longitude,
  });

  @override
  State<AddressFormScreen> createState() => _AddressFormScreenState();
}

class _AddressFormScreenState extends State<AddressFormScreen> {
  final _formKey = GlobalKey<FormState>();

  final _addressLine1Controller = TextFieldManager();
  final _landmarkController = TextFieldManager();
  final _cityController = TextFieldManager();
  final _stateController = TextFieldManager();
  final _pincodeController = TextFieldManager();
  final _searchController = TextFieldManager();

  bool _isDefault = false;
  String _addressType = 'home';

  double _latitude = 0.0;
  double _longitude = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  @override
  void dispose() {
    _addressLine1Controller.dispose();
    _landmarkController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _initializeForm() {
    if (widget.address != null) {
      _populateFormWithAddress(widget.address!);
    } else if (widget.latitude != null && widget.longitude != null) {
      // If coordinates are passed from MapScreen, use them and get address
      setState(() {
        _latitude = widget.latitude!;
        _longitude = widget.longitude!;
      });
      // Get address details from coordinates using BLoC
      context.read<LocationBloc>().add(
            LocationEvent.getAddressFromCoordinates(
              widget.latitude!,
              widget.longitude!,
            ),
          );
    } else {
      // For new addresses, try to get current location using BLoC
      context
          .read<LocationBloc>()
          .add(const LocationEvent.getCurrentLocation());
    }
  }

  void _populateFormWithAddress(AddressModel address) {
    _addressLine1Controller.text = address.addressLine1 ?? '';
    _landmarkController.text = address.landmark ?? '';
    _cityController.text = address.city ?? '';
    _stateController.text = address.state ?? '';
    _pincodeController.text = address.pincode ?? '';
    _isDefault = address.isDefault ?? false;
    _addressType = address.addressType ?? 'home';
    _latitude = (address.latitude ?? 0).toDouble();
    _longitude = (address.longitude ?? 0).toDouble();
  }

  Future<void> _navigateToLocationSelection() async {
    final result = await context.push(
      RouteNames.mapForEditAddress,
      extra: {widget.address},
    );

    if (result != null && result is Map<String, dynamic>) {
      final latitude = result['latitude'] as double?;
      final longitude = result['longitude'] as double?;

      if (latitude != null && longitude != null) {
        setState(() {
          _latitude = latitude;
          _longitude = longitude;
        });
      }
    }
  }

  void _saveAddress() {
    // Validate that coordinates are set (not default 0.0, 0.0)
    if (_latitude == 0.0 && _longitude == 0.0) {
      _showSnackBar(
          'Please select a location on the map or allow location access');
      return;
    }

    final addressId =
        widget.address?.id ?? DateTime.now().millisecondsSinceEpoch.toString();

    final address = AddressModel(
      id: addressId,
      fullAddress: _buildFullAddress(),
      addressLine1: _addressLine1Controller.text,
      landmark:
          _landmarkController.text.isNotEmpty ? _landmarkController.text : null,
      city: _cityController.text,
      state: _stateController.text,
      pincode: _pincodeController.text,
      latitude: _latitude,
      longitude: _longitude,
      addressType: _addressType,
      isDefault: _isDefault,
    );

    // Save address using BLoC
    if (widget.address != null) {
      // Edit existing address
      context.read<LocationBloc>().add(LocationEvent.editAddress(address));
    } else {
      // Add new address
      context.read<LocationBloc>().add(LocationEvent.addAddress(address));
    }
  }

  String _buildFullAddress() {
    final parts = [
      _addressLine1Controller.text,
      _landmarkController.text,
      _cityController.text,
      _stateController.text,
      _pincodeController.text,
    ];

    return parts.where((part) => part.isNotEmpty).join(', ');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
      ),
    );
  }

  void _autoPopulateFromAddress(String addressString) {
    // Simple parsing of address string to populate form fields
    // This is a basic implementation - you might want to enhance it
    final parts = addressString.split(', ');
    if (parts.isNotEmpty) {
      _addressLine1Controller.text = parts.first;
    }
    if (parts.length > 1) {
      _cityController.text = parts[1];
    }
    if (parts.length > 2) {
      _stateController.text = parts[2];
    }
    if (parts.length > 3) {
      _pincodeController.text = parts.last;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LocationBloc, LocationState>(
      listener: (context, state) {
        state.maybeWhen(
          locationDetected: (latitude, longitude, address) {
            setState(() {
              _latitude = latitude;
              _longitude = longitude;
            });
            // Auto-populate form fields from detected address
            _autoPopulateFromAddress(address);
          },
          addressSaved: (address) {
            // Navigate based on the flow
            if (widget.fromCart) {
              context.go(RouteNames.cart);
            } else {
              context.go(RouteNames.home);
            }
          },
          error: (message) {
            _showSnackBar(message);
          },
          orElse: () {},
        );
      },
      child: BlocBuilder<LocationBloc, LocationState>(
        builder: (context, state) {
          final isLoading = state.maybeWhen(
            loading: () => true,
            orElse: () => false,
          );

          return Scaffold(
            appBar: AppBar(
              title: Text(widget.address != null
                  ? 'Edit Address'
                  : 'Add Address Details'),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: AppColors.primary,
                statusBarIconBrightness: Brightness.light,
              ),
            ),
            body: _buildForm(isLoading),
          );
        },
      ),
    );
  }

  Widget _buildForm(bool isLoading) {
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Select location on map button
          AppButton(
            text: 'Select Location on Map',
            onPressed: _navigateToLocationSelection,
            isOutlined: true,
            prefixIcon: const Icon(Icons.map, size: 18),
          ),
          const SizedBox(height: 8),

          // Location status indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: (_latitude != 0.0 || _longitude != 0.0)
                  ? Colors.green.shade50
                  : Colors.orange.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: (_latitude != 0.0 || _longitude != 0.0)
                    ? Colors.green.shade200
                    : Colors.orange.shade200,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  (_latitude != 0.0 || _longitude != 0.0)
                      ? Icons.location_on
                      : Icons.location_off,
                  size: 16,
                  color: (_latitude != 0.0 || _longitude != 0.0)
                      ? Colors.green.shade700
                      : Colors.orange.shade700,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    (_latitude != 0.0 || _longitude != 0.0)
                        ? 'Location detected (${_latitude.toStringAsFixed(4)}, ${_longitude.toStringAsFixed(4)})'
                        : 'Location not set - please select on map or allow location access',
                    style: TextStyle(
                      fontSize: 12,
                      color: (_latitude != 0.0 || _longitude != 0.0)
                          ? Colors.green.shade700
                          : Colors.orange.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Address type selector
          Text(
            'Address Type',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          _buildAddressTypeSelector(),
          const SizedBox(height: 24),

          // Address form fields
          ValueListenableBuilder(
              valueListenable: _addressLine1Controller.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'Address Line',
                  field: CustomTextField(
                    hintText: 'House/Flat No., Building, Street',
                    controller: _addressLine1Controller.controller,
                    // focusNode: _addressLine1Controller.focusNode,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.home),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),

          Titledfield(
            title: 'Landmark (Optional)',
            field: CustomTextField(
                hintText: 'Nearby landmark',
                controller: _landmarkController.controller,
                // focusNode: _landmarkController.focusNode,
                decoration: InputDecoration(
                  prefixIcon: const Icon(Icons.location_on),
                )),
          ),
          const SizedBox(height: 16),

          ValueListenableBuilder(
              valueListenable: _cityController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'City',
                  field: CustomTextField(
                    hintText: 'City',
                    controller: _cityController.controller,
                    // focusNode: _cityController.focusNode,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.location_city),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),

          ValueListenableBuilder(
              valueListenable: _stateController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'State',
                  field: CustomTextField(
                    hintText: 'State',
                    controller: _stateController.controller,
                    // focusNode: _stateController.focusNode,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.map),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),

          ValueListenableBuilder(
              valueListenable: _pincodeController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'Pincode',
                  field: CustomTextField(
                      hintText: 'Pincode',
                      controller: _pincodeController.controller,
                      // focusNode: _pincodeController.focusNode,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(6),
                      ],
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.pin_drop),
                      )),
                  errorText: error,
                );
              }),
          const SizedBox(height: 24),

          // Default address checkbox
          Row(
            children: [
              Checkbox(
                value: _isDefault,
                onChanged: (value) {
                  setState(() {
                    _isDefault = value ?? false;
                  });
                },
                activeColor: AppColors.primary,
              ),
              const Text('Set as default address'),
            ],
          ),
          const SizedBox(height: 24),

          // Save button
          AppButton(
            text: 'Save Address',
            onPressed: () {
              ValidationState addressValidationState =
                  AppValidator.emptyStringValidator(
                      _addressLine1Controller.text, 'Please enter address');

              ValidationState cityValidationState =
                  AppValidator.emptyStringValidator(
                      _cityController.text, 'Please enter city');
              ValidationState stateValidationState =
                  AppValidator.emptyStringValidator(
                      _stateController.text, 'Please enter state');
              ValidationState pincodeValidationState =
                  AppValidator.emptyStringValidator(
                      _pincodeController.text, 'Please enter pincode',
                      minLength: 6,
                      lengthMessage: 'Please enter a valid 6-digit pincode');

              if (!addressValidationState.valid) {
                _addressLine1Controller
                    .throwError(addressValidationState.message ?? '');
                return;
              }
              _addressLine1Controller.throwError('');
              if (!cityValidationState.valid) {
                _cityController.throwError(cityValidationState.message ?? '');
                return;
              }
              _cityController.throwError('');
              if (!stateValidationState.valid) {
                _stateController.throwError(stateValidationState.message ?? '');
                return;
              }
              _stateController.throwError('');
              if (!pincodeValidationState.valid) {
                _pincodeController
                    .throwError(pincodeValidationState.message ?? '');
                return;
              }
              _pincodeController.throwError('');
              _saveAddress();
            },
            isLoading: isLoading,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildAddressTypeSelector() {
    return Row(
      children: [
        _buildAddressTypeOption('home', 'Home', Icons.home),
        const SizedBox(width: 16),
        _buildAddressTypeOption('work', 'Work', Icons.work),
        const SizedBox(width: 16),
        _buildAddressTypeOption('other', 'Other', Icons.place),
      ],
    );
  }

  Widget _buildAddressTypeOption(String type, String label, IconData icon) {
    final isSelected = _addressType == type;

    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            _addressType = type;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppColors.primary : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected
                ? AppColors.primary.withValues(alpha: 0.1)
                : Colors.transparent,
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? AppColors.primary : Colors.grey,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? AppColors.primary : Colors.grey.shade700,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
