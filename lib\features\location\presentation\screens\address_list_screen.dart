import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_button.dart';
import '../../bloc/location bloc/location_bloc.dart';

class AddressListScreen extends StatelessWidget {
  final bool selectMode;
  final Function(AddressModel)? onAddressSelected;

  const AddressListScreen({
    super.key,
    this.selectMode = false,
    this.onAddressSelected,
  });

  @override
  Widget build(BuildContext context) {
    // Load addresses when screen is built
    context.read<LocationBloc>().add(const LocationEvent.loadAddresses());

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.primaryAverage),
          onPressed: () => context.pop(),
        ),
        title: Text(
          selectMode ? 'Select Address' : 'Saved Addresses',
          style: const TextStyle(
            color: AppColors.primaryAverage,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: BlocConsumer<LocationBloc, LocationState>(
        listener: (context, state) {
          state.maybeWhen(
            error: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(message),
                  backgroundColor: Colors.red,
                ),
              );
            },
            addressDeleted: (addressId) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Address deleted successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            defaultAddressSet: (addressId) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Default address updated'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            orElse: () {},
          );
        },
        builder: (context, state) {
          return state.maybeWhen(
            loading: () => const Center(
              child: CircularProgressIndicator(color: AppColors.primaryDark),
            ),
            addressesLoaded: (addresses, defaultAddressId) => _buildAddressList(
              context,
              addresses,
              defaultAddressId,
            ),
            error: (message) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    message,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<LocationBloc>().add(const LocationEvent.loadAddresses());
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
            orElse: () => const Center(
              child: CircularProgressIndicator(color: AppColors.primaryDark),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddAddress(context),
        backgroundColor: AppColors.primaryAverage,
        child: const Icon(Icons.add, color: AppColors.background),
      ),
    );
  }

  void _navigateToAddAddress(BuildContext context) {
    context.push(RouteNames.mapForNewAddress).then((_) {
      // Reload addresses after returning from add screen
      context.read<LocationBloc>().add(const LocationEvent.loadAddresses());
    });
  }

  void _navigateToEditAddress(BuildContext context, AddressModel address) {
    context.push(RouteNames.mapForEditAddress, extra: address).then((_) {
      // Reload addresses after returning from edit screen
      context.read<LocationBloc>().add(const LocationEvent.loadAddresses());
    });
  }

  Widget _buildAddressList(
    BuildContext context,
    List<AddressModel> addresses,
    String? defaultAddressId,
  ) {
    if (addresses.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: addresses.length,
      itemBuilder: (context, index) {
        final address = addresses[index];
        final isDefault = address.id == defaultAddressId;
        return _buildAddressCard(context, address, isDefault);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.location_off,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'No addresses saved',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Add your first address to get started',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          AppButton(
            text: 'Add Address',
            onPressed: () => _navigateToAddAddress(context),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressCard(
    BuildContext context,
    AddressModel address,
    bool isDefault,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isDefault
            ? const BorderSide(color: AppColors.primaryAverage, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: selectMode
            ? () {
                if (onAddressSelected != null) {
                  onAddressSelected!(address);
                }
                context.pop();
              }
            : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildAddressTypeTag(address.addressType ?? ''),
                  const SizedBox(width: 8),
                  if (isDefault)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primaryAverage.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        'DEFAULT',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryAverage,
                        ),
                      ),
                    ),
                  const Spacer(),
                  if (!selectMode) ...[
                    IconButton(
                      onPressed: () => _navigateToEditAddress(context, address),
                      icon: const Icon(
                        Icons.edit,
                        size: 18,
                        color: Colors.grey,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 36,
                        minHeight: 36,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                    IconButton(
                      onPressed: () => _showDeleteConfirmation(context, address),
                      icon: const Icon(
                        Icons.delete,
                        size: 18,
                        color: Colors.red,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 36,
                        minHeight: 36,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 8),
              Text(
                address.fullAddress ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
              if (address.landmark != null && address.landmark!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  'Landmark: ${address.landmark}',
                  style: const TextStyle(
                    fontSize: 13,
                    color: Colors.grey,
                  ),
                ),
              ],
              const SizedBox(height: 12),
              if (!selectMode && !isDefault)
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () {
                      context.read<LocationBloc>().add(
                        LocationEvent.setDefaultAddress(address.id ?? ''),
                      );
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primaryAverage,
                      side: const BorderSide(color: AppColors.primaryAverage),
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Set as Default'),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddressTypeTag(String addressType) {
    IconData icon;
    Color color;

    switch (addressType.toLowerCase()) {
      case 'home':
        icon = Icons.home;
        color = Colors.green;
        break;
      case 'work':
        icon = Icons.work;
        color = Colors.blue;
        break;
      case 'other':
        icon = Icons.location_on;
        color = Colors.orange;
        break;
      default:
        icon = Icons.location_on;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            addressType.toUpperCase(),
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, AddressModel address) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Delete Address'),
          content: Text('Are you sure you want to delete "${address.addressLine1}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                context.read<LocationBloc>().add(
                  LocationEvent.deleteAddress(address.id ?? ''),
                );
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }
}
