import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../data/models/adress_model.dart';

part 'location_event.freezed.dart';

@freezed
class LocationEvent with _$LocationEvent {
  // Core location events
  const factory LocationEvent.started() = _Started;
  
  const factory LocationEvent.refreshLocation() = _RefreshLocation;

  // Address management events
  const factory LocationEvent.loadAddresses() = _LoadAddresses;
  const factory LocationEvent.addAddress(AddressModel address) = _AddAddress;
  const factory LocationEvent.editAddress(AddressModel address) = _EditAddress;
  const factory LocationEvent.deleteAddress(String addressId) = _DeleteAddress;
  const factory LocationEvent.setDefaultAddress(String addressId) = _SetDefaultAddress;

  // Location detection events
  const factory LocationEvent.getCurrentLocation() = _GetCurrentLocation;
  const factory LocationEvent.getAddressFromCoordinates(double latitude, double longitude) = _GetAddressFromCoordinates;

  // Map and search events
  const factory LocationEvent.searchLocation(String query) = _SearchLocation;
  const factory LocationEvent.selectLocationOnMap(double latitude, double longitude) = _SelectLocationOnMap;

  // Form management events
  const factory LocationEvent.updateFormField(String fieldName, String value) = _UpdateFormField;
  const factory LocationEvent.validateForm() = _ValidateForm;
  const factory LocationEvent.resetForm() = _ResetForm;
  const factory LocationEvent.setAddressType(String addressType) = _SetAddressType;
  const factory LocationEvent.setDefaultFlag(bool isDefault) = _SetDefaultFlag;
}
