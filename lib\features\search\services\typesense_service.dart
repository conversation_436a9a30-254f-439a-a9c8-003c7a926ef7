import 'package:flutter/material.dart';
import 'package:typesense/typesense.dart';

/// Configuration for Typesense search service
class TypesenseConfig {
  /// API key for Typesense
  static const String apiKey = 'WzQSB1f8gpXWmsQzRjvEO149v04bfaJa';

  /// Host for Typesense server (without port)
  static const String host = 'rozanats.headrun.com';

  /// Port for Typesense server
  static const String port = '8443';

  /// Protocol for Typesense server
  static const String protocol = 'https';

  /// Connection timeout in seconds
  static const int connectionTimeoutSeconds = 10;

  /// Collection names in Typesense
  static const String categoriesCollection = 'categories_latest';
  static const String subCategoriesCollection = 'subcategories';
  static const String productsCollection = 'products';
}

class TypesenseService {
  late final Client _client;

  static final TypesenseService _instance = TypesenseService._internal();

  factory TypesenseService() => _instance;

  TypesenseService._internal() {
    _initializeClient();
  }

  /// Initialize the Typesense client
  void _initializeClient() {
    _client = Client(
      Configuration(TypesenseConfig.apiKey,
          nodes: {
            Node(
              TypesenseConfig.protocol == 'https'
                  ? Protocol.https
                  : Protocol.http,
              TypesenseConfig.host,
            ),
          },
          connectionTimeout:
              Duration(seconds: TypesenseConfig.connectionTimeoutSeconds),
          numRetries: 3),
    );
  }

  /// Search for categories
  Future<List<Map<String, dynamic>>> searchCategories({
    String query = '*',
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final searchParameters = {
        'q': query,
        'query_by': 'name',
        'page': page.toString(),
        'per_page': pageSize.toString(),
      };
      final searchResult = await _client
          .collection(TypesenseConfig.categoriesCollection)
          .documents
          .search(searchParameters);
      return _processCategoryResults(searchResult);
    } catch (e) {
      debugPrint('Error searching categories: $e');
      debugPrint('Error type: ${e.runtimeType}');
      if (e is Exception) {
        debugPrint('Exception details: ${e.toString()}');
      }
      return [];
    }
  }

  /// Search for subcategories by category ID
  Future<List<Map<String, dynamic>>> searchSubCategories({
    String categoryID = '',
    String query = '*',
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final Map<String, String> searchParameters = {
        'q': query,
        'query_by': 'name',
        'page': page.toString(),
        'per_page': pageSize.toString(),
      };

      // Add filter if categoryID is provided
      if (categoryID.isNotEmpty) {
        searchParameters['filter_by'] = 'category_id:=$categoryID';
      }

      final searchResult = await _client
          .collection(TypesenseConfig.subCategoriesCollection)
          .documents
          .search(searchParameters);

      return _processCategoryResults(searchResult);
    } catch (e) {
      debugPrint('Error searching subcategories: $e');
      return [];
    }
  }

  /// Search for products by subcategory ID
  Future<List<Map<String, dynamic>>> searchProducts({
    String subCategoryID = '',
    String categoryID = '',
    String query = '*',
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final Map<String, String> searchParameters = {
        'q': query,
        'query_by': 'name,description',
        'page': page.toString(),
        'per_page': pageSize.toString()
      };

      // Only add filter if subCategoryID is provided
      if (subCategoryID.isNotEmpty) {
        searchParameters['filter_by'] = subCategoryID == 'All'
            ? 'categories:="$categoryID"'
            : 'subcategories:="$subCategoryID"';
      } else if (categoryID.isNotEmpty) {
        searchParameters['filter_by'] = 'categories:="$categoryID"';
      }

      final searchResult = await _client
          .collection(TypesenseConfig.productsCollection)
          .documents
          .search(searchParameters);

      return _processSearchResults(searchResult);
    } catch (e) {
      debugPrint('Error searching products: $e');
      return [];
    }
  }

  /// Global search across products, categories, and subcategories
  Future<Map<String, List<Map<String, dynamic>>>> globalSearch({
    required String query,
    int page = 1,
    int pageSize = 20,
  }) async {
    if (query.isEmpty) {
      return {
        'products': [],
        'categories': [],
        'subcategories': [],
      };
    }

    try {
      // Search for products
      final products = await searchProducts(
        query: query,
        page: page,
        pageSize: pageSize,
      );

      // Search for categories (using facets)
      final categoriesResult = await _client
          .collection(TypesenseConfig.productsCollection)
          .documents
          .search({
        'q': query,
        'query_by': 'name',
        'facet_by': 'categories',
        'max_facet_values': '5',
        'page': '1',
        'per_page': '0',
      });

      final categories = _processFacetResults(categoriesResult, 'categories');

      // Search for subcategories (using facets)
      final subcategoriesResult = await _client
          .collection(TypesenseConfig.productsCollection)
          .documents
          .search({
        'q': query,
        'query_by': 'name',
        'facet_by': 'subcategories',
        'max_facet_values': '5',
        'page': '1',
        'per_page': '0',
      });

      final subcategories =
          _processFacetResults(subcategoriesResult, 'subcategories');

      return {
        'products': products,
        'categories': categories,
        'subcategories': subcategories,
      };
    } catch (e) {
      debugPrint('Error in global search: $e');
      return {
        'products': [],
        'categories': [],
        'subcategories': [],
      };
    }
  }

  /// Get search suggestions based on user input
  Future<List<Map<String, dynamic>>> getSearchSuggestions(String query) async {
    if (query.isEmpty) {
      return [];
    }

    try {
      final searchResult = await _client
          .collection(TypesenseConfig.productsCollection)
          .documents
          .search({
        'q': query,
        'query_by':
            'name,brand,categories,subcategories,description,display_alias,colour,tags',
        "highlight_fields": [],
        'include_fields': 'name,thumbnail_url,display_alias',
        'num_typos': '2',
        'prefix': 'true',
        'per_page': '10',
      });
      final data = _processSearchResults(searchResult);

      // Extract product names as suggestions from processed data
      final suggestions = data
          .map((product) => {
                'name': product['name'],
                'imageUrl': product['imageUrl'],
              })
          .toList();

      // Add the original query as the first suggestion
      if (!suggestions.any((suggestion) => suggestion['name'] == query)) {
        suggestions.insert(0, {
          'name': query,
          'imageUrl': 'assets/products/search.png',
        });
      }

      return suggestions;
    } catch (e) {
      debugPrint('Error getting search suggestions: $e');
      return [
        {
          'name': query,
          'imageUrl': 'assets/products/search.png',
        }
      ]; // Return the original query as fallback
    }
  }

  /// Process category search results from Typesense
  List<Map<String, dynamic>> _processCategoryResults(
      Map<String, dynamic> searchResult) {
    final hits = searchResult['hits'] as List<dynamic>?;

    if (hits == null || hits.isEmpty) {
      return [];
    }

    return hits.map((hit) {
      final document = hit['document'] as Map<String, dynamic>;
      // Map to match CategoryCard requirements
      return {
        'id': document['id'] ?? document['name'] ?? '',
        'objectID': document['id'] ?? document['name'] ?? '',
        'name': document['name'] ?? '',
        'count': document['product_count'] ?? document['count'] ?? 0,
        'imageUrl':
            document['thumbnail_url'] ?? 'assets/categories/shopping-bag.png',
        'icon': null,
        'iconColor': null,
      };
    }).toList();
  }

  /// Process search results from Typesense
  List<Map<String, dynamic>> _processSearchResults(
      Map<String, dynamic> searchResult) {
    final hits = searchResult['hits'] as List<dynamic>?;

    if (hits == null || hits.isEmpty) {
      return [];
    }
    return hits.map((hit) {
      final document = hit['document'] as Map<String, dynamic>;
      debugPrint(document.toString());
      // Map to match ProductCard requirements

      return {
        'id': document['id'] ?? '',
        'objectID': document['id'] ?? '',
        'name': document['display_alias'] != null &&
                document['display_alias'].isNotEmpty
            ? document['display_alias'][0]
            : document['name'],
        'description': document['description'] ?? '',
        'imageUrl': (document['thumbnail_url'] == null ||
                document['thumbnail_url'] == '\\N')
            ? 'assets/products/vegetables.png'
            : document['thumbnail_url'],
        'price': (document['price'] is int)
            ? (document['price'] as int).toDouble()
            : (document['price'] ?? 0.0),
        'originalPrice': null,
        'rating': null,
        'reviewCount': null,
        'isInWishlist': false,
        'isOutOfStock': false,
        'discountLabel': null,
        'category': document['categories']?.isNotEmpty == true
            ? document['categories'][0]
            : '',
        'subcategory': document['subcategories']?.isNotEmpty == true
            ? document['subcategories'][0]
            : '',
        'facilityId': document['facility_id'] ?? '',
        'facilityName': document['facility_name'] ?? '',
        'sku': document['sku'] ?? '',
      };
    }).toList();
  }

  /// Process facet results from Typesense
  List<Map<String, dynamic>> _processFacetResults(
      Map<String, dynamic> searchResult, String facetBy) {
    final counts = searchResult['hits'] as List<dynamic>;

    if (counts.isEmpty) {
      return [];
    }

    // Convert facet counts to a list of maps
    return counts.map((count) {
      final document = count['document'] as Map<String, dynamic>;
      // Map to match CategoryCard requirements
      return {
        'id': document['id'],
        'objectID': document['name'],
        'name': document['name'],
        'count': count['count'] ?? 0,
        'imageUrl':
            document['thumbnail_url'] ?? 'assets/categories/shopping-bag.png',
        'icon': null,
        'iconColor': Colors.blue[300],
      };
    }).toList();
  }
}
