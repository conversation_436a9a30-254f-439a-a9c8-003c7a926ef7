import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/routes/app_router.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_button.dart';

class MapScreen extends StatefulWidget {
  final AddressModel? address;
  final Function(double latitude, double longitude)? onLocationSelected;
  final bool fromCart;
  const MapScreen({
    super.key,
    this.address,
    this.fromCart = false,
    this.onLocationSelected,
  });

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  GoogleMapController? _mapController;
  double _currentLatitude = 28.6139; // Default to Delhi
  double _currentLongitude = 77.2090;
  String _currentAddress = 'Loading address...';

  // Search controller
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  List<AddressModel> _searchResults = [];

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  void _initializeLocation() {
    if (widget.address != null &&
        widget.address!.latitude != null &&
        widget.address!.longitude != null) {
      // Initialize with existing address coordinates
      setState(() {
        _currentLatitude = (widget.address!.latitude as double);
        _currentLongitude = (widget.address!.longitude as double);
      });
      // Get address from coordinates using BLoC
      context.read<LocationBloc>().add(
            LocationEvent.getAddressFromCoordinates(
              _currentLatitude,
              _currentLongitude,
            ),
          );
    } else {
      // Get current location using BLoC
      context
          .read<LocationBloc>()
          .add(const LocationEvent.getCurrentLocation());
    }
  }

  void _onCameraMove(CameraPosition position) {
    // Update coordinates as map moves
    setState(() {
      _currentLatitude = position.target.latitude;
      _currentLongitude = position.target.longitude;
    });
  }

  void _onCameraIdle() {
    // Trigger reverse geocoding using BLoC
    context.read<LocationBloc>().add(
          LocationEvent.getAddressFromCoordinates(
            _currentLatitude,
            _currentLongitude,
          ),
        );
  }

  void _getCurrentLocation() {
    // Dispatch getCurrentLocation event to BLoC
    context.read<LocationBloc>().add(const LocationEvent.getCurrentLocation());
  }

  void _selectLocation() {
    if (widget.onLocationSelected != null) {
      widget.onLocationSelected!(_currentLatitude, _currentLongitude);
      if (mounted) {
        context.pop({
          'latitude': _currentLatitude,
          'longitude': _currentLongitude,
        });
      }
      return;
    }

    // Navigate to address form with coordinates
    if (mounted) {
      context.push(
        RouteNames.editAddress,
        extra: {
          'latitude': _currentLatitude,
          'longitude': _currentLongitude,
          'fromCart': widget.fromCart,
        },
      );
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _searchLocation(String query) {
    if (query.trim().isEmpty) return;

    // Dispatch search event to BLoC
    context.read<LocationBloc>().add(LocationEvent.searchLocation(query));
  }

  void _selectSearchResult(AddressModel result) {
    if (result.latitude != null && result.longitude != null) {
      setState(() {
        _currentLatitude = (result.latitude as double);
        _currentLongitude = (result.longitude as double);
        _currentAddress = result.fullAddress ?? 'Selected location';
        _searchResults = [];
        _isSearching = false;
      });

      // Animate camera to selected location
      _mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(_currentLatitude, _currentLongitude),
            zoom: 16,
          ),
        ),
      );

      // Clear search
      _searchController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LocationBloc, LocationState>(
      listener: (context, state) {
        state.maybeWhen(
          locationDetected: (latitude, longitude, address) {
            setState(() {
              _currentLatitude = latitude;
              _currentLongitude = longitude;
              _currentAddress = address;
            });
            // Animate camera to detected location
            _mapController?.animateCamera(
              CameraUpdate.newCameraPosition(
                CameraPosition(
                  target: LatLng(latitude, longitude),
                  zoom: 16,
                ),
              ),
            );
          },
          searchResults: (results) {
            setState(() {
              _searchResults = results;
              _isSearching = false;
            });
          },
          error: (message) {
            _showSnackBar(message);
            setState(() {
              _isSearching = false;
            });
          },
          orElse: () {},
        );
      },
      child: BlocBuilder<LocationBloc, LocationState>(
        builder: (context, state) {
          final isLoading = state.maybeWhen(
            loading: () => true,
            orElse: () => false,
          );

          // Update address from mapState if available
          state.maybeWhen(
            mapState: (mapData) {
              if (mapData.address.isNotEmpty &&
                  mapData.address != _currentAddress) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  setState(() {
                    _currentAddress = mapData.address;
                  });
                });
              }
            },
            orElse: () {},
          );

          return Scaffold(
            appBar: AppBar(
              title: const Text('Select Location'),
              backgroundColor: AppColors.primaryAverage,
              foregroundColor: Colors.white,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: AppColors.primary,
                statusBarIconBrightness: Brightness.light,
              ),
              actions: [
                IconButton(
                  onPressed: isLoading ? null : _getCurrentLocation,
                  icon: isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.my_location),
                  tooltip: 'Get Current Location',
                ),
              ],
            ),
            body: Stack(
              children: [
                // Google Map
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: LatLng(_currentLatitude, _currentLongitude),
                    zoom: 16,
                  ),
                  onMapCreated: (controller) {
                    _mapController = controller;
                  },
                  onCameraMove: _onCameraMove,
                  onCameraIdle: _onCameraIdle,
                  myLocationEnabled: true,
                  myLocationButtonEnabled: false, // We have our own button
                  zoomControlsEnabled: true,
                  mapToolbarEnabled: false,
                ),

                // Search bar at the top
                Positioned(
                  top: 20,
                  left: 16,
                  right: 16,
                  child: Material(
                    elevation: 4,
                    borderRadius: BorderRadius.circular(8),
                    child: TextField(
                      controller: _searchController,
                      textInputAction: TextInputAction.search,
                      onSubmitted: _searchLocation,
                      decoration: InputDecoration(
                        hintText: 'Search for a place or address',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _isSearching
                            ? const Padding(
                                padding: EdgeInsets.all(10),
                                child: SizedBox(
                                  width: 16,
                                  height: 16,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                ),
                              )
                            : (_searchController.text.isNotEmpty
                                ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () {
                                      _searchController.clear();
                                    },
                                  )
                                : null),
                        border: InputBorder.none,
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 14),
                      ),
                    ),
                  ),
                ),

                // Search results
                if (_searchResults.isNotEmpty)
                  Positioned(
                    top: 120,
                    left: 16,
                    right: 16,
                    child: Container(
                      constraints: const BoxConstraints(maxHeight: 200),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: const [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 10,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: _searchResults.length,
                        itemBuilder: (context, index) {
                          final result = _searchResults[index];
                          return ListTile(
                            leading: const Icon(Icons.location_on),
                            title:
                                Text(result.fullAddress ?? 'Unknown location'),
                            onTap: () => _selectSearchResult(result),
                          );
                        },
                      ),
                    ),
                  ),

                // Static marker in center
                const Center(
                  child: Icon(
                    Icons.location_pin,
                    size: 40,
                    color: AppColors.primaryAverage,
                  ),
                ),

                // Address info card
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 34),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 10,
                          offset: Offset(0, -2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          'Selected Location',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryAverage,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _currentAddress,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 16),
                        CustomButton(
                          text: 'Select This Location',
                          onPressed: _selectLocation,
                          backgroundColor: AppColors.primaryAverage,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
