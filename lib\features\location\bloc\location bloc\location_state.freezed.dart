// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LocationFormData {
  String get addressLine1 => throw _privateConstructorUsedError;
  String get landmark => throw _privateConstructorUsedError;
  String get city => throw _privateConstructorUsedError;
  String get state => throw _privateConstructorUsedError;
  String get pincode => throw _privateConstructorUsedError;
  String get addressType => throw _privateConstructorUsedError;
  bool get isDefault => throw _privateConstructorUsedError;
  double get latitude => throw _privateConstructorUsedError;
  double get longitude => throw _privateConstructorUsedError;
  Map<String, String> get fieldErrors => throw _privateConstructorUsedError;
  bool get isValid => throw _privateConstructorUsedError;

  /// Create a copy of LocationFormData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationFormDataCopyWith<LocationFormData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationFormDataCopyWith<$Res> {
  factory $LocationFormDataCopyWith(
          LocationFormData value, $Res Function(LocationFormData) then) =
      _$LocationFormDataCopyWithImpl<$Res, LocationFormData>;
  @useResult
  $Res call(
      {String addressLine1,
      String landmark,
      String city,
      String state,
      String pincode,
      String addressType,
      bool isDefault,
      double latitude,
      double longitude,
      Map<String, String> fieldErrors,
      bool isValid});
}

/// @nodoc
class _$LocationFormDataCopyWithImpl<$Res, $Val extends LocationFormData>
    implements $LocationFormDataCopyWith<$Res> {
  _$LocationFormDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationFormData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressLine1 = null,
    Object? landmark = null,
    Object? city = null,
    Object? state = null,
    Object? pincode = null,
    Object? addressType = null,
    Object? isDefault = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? fieldErrors = null,
    Object? isValid = null,
  }) {
    return _then(_value.copyWith(
      addressLine1: null == addressLine1
          ? _value.addressLine1
          : addressLine1 // ignore: cast_nullable_to_non_nullable
              as String,
      landmark: null == landmark
          ? _value.landmark
          : landmark // ignore: cast_nullable_to_non_nullable
              as String,
      city: null == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String,
      pincode: null == pincode
          ? _value.pincode
          : pincode // ignore: cast_nullable_to_non_nullable
              as String,
      addressType: null == addressType
          ? _value.addressType
          : addressType // ignore: cast_nullable_to_non_nullable
              as String,
      isDefault: null == isDefault
          ? _value.isDefault
          : isDefault // ignore: cast_nullable_to_non_nullable
              as bool,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      fieldErrors: null == fieldErrors
          ? _value.fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocationFormDataImplCopyWith<$Res>
    implements $LocationFormDataCopyWith<$Res> {
  factory _$$LocationFormDataImplCopyWith(_$LocationFormDataImpl value,
          $Res Function(_$LocationFormDataImpl) then) =
      __$$LocationFormDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String addressLine1,
      String landmark,
      String city,
      String state,
      String pincode,
      String addressType,
      bool isDefault,
      double latitude,
      double longitude,
      Map<String, String> fieldErrors,
      bool isValid});
}

/// @nodoc
class __$$LocationFormDataImplCopyWithImpl<$Res>
    extends _$LocationFormDataCopyWithImpl<$Res, _$LocationFormDataImpl>
    implements _$$LocationFormDataImplCopyWith<$Res> {
  __$$LocationFormDataImplCopyWithImpl(_$LocationFormDataImpl _value,
      $Res Function(_$LocationFormDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationFormData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressLine1 = null,
    Object? landmark = null,
    Object? city = null,
    Object? state = null,
    Object? pincode = null,
    Object? addressType = null,
    Object? isDefault = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? fieldErrors = null,
    Object? isValid = null,
  }) {
    return _then(_$LocationFormDataImpl(
      addressLine1: null == addressLine1
          ? _value.addressLine1
          : addressLine1 // ignore: cast_nullable_to_non_nullable
              as String,
      landmark: null == landmark
          ? _value.landmark
          : landmark // ignore: cast_nullable_to_non_nullable
              as String,
      city: null == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String,
      pincode: null == pincode
          ? _value.pincode
          : pincode // ignore: cast_nullable_to_non_nullable
              as String,
      addressType: null == addressType
          ? _value.addressType
          : addressType // ignore: cast_nullable_to_non_nullable
              as String,
      isDefault: null == isDefault
          ? _value.isDefault
          : isDefault // ignore: cast_nullable_to_non_nullable
              as bool,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      fieldErrors: null == fieldErrors
          ? _value._fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$LocationFormDataImpl implements _LocationFormData {
  const _$LocationFormDataImpl(
      {this.addressLine1 = '',
      this.landmark = '',
      this.city = '',
      this.state = '',
      this.pincode = '',
      this.addressType = 'home',
      this.isDefault = false,
      this.latitude = 0.0,
      this.longitude = 0.0,
      final Map<String, String> fieldErrors = const {},
      this.isValid = false})
      : _fieldErrors = fieldErrors;

  @override
  @JsonKey()
  final String addressLine1;
  @override
  @JsonKey()
  final String landmark;
  @override
  @JsonKey()
  final String city;
  @override
  @JsonKey()
  final String state;
  @override
  @JsonKey()
  final String pincode;
  @override
  @JsonKey()
  final String addressType;
  @override
  @JsonKey()
  final bool isDefault;
  @override
  @JsonKey()
  final double latitude;
  @override
  @JsonKey()
  final double longitude;
  final Map<String, String> _fieldErrors;
  @override
  @JsonKey()
  Map<String, String> get fieldErrors {
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fieldErrors);
  }

  @override
  @JsonKey()
  final bool isValid;

  @override
  String toString() {
    return 'LocationFormData(addressLine1: $addressLine1, landmark: $landmark, city: $city, state: $state, pincode: $pincode, addressType: $addressType, isDefault: $isDefault, latitude: $latitude, longitude: $longitude, fieldErrors: $fieldErrors, isValid: $isValid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationFormDataImpl &&
            (identical(other.addressLine1, addressLine1) ||
                other.addressLine1 == addressLine1) &&
            (identical(other.landmark, landmark) ||
                other.landmark == landmark) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.pincode, pincode) || other.pincode == pincode) &&
            (identical(other.addressType, addressType) ||
                other.addressType == addressType) &&
            (identical(other.isDefault, isDefault) ||
                other.isDefault == isDefault) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            const DeepCollectionEquality()
                .equals(other._fieldErrors, _fieldErrors) &&
            (identical(other.isValid, isValid) || other.isValid == isValid));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      addressLine1,
      landmark,
      city,
      state,
      pincode,
      addressType,
      isDefault,
      latitude,
      longitude,
      const DeepCollectionEquality().hash(_fieldErrors),
      isValid);

  /// Create a copy of LocationFormData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationFormDataImplCopyWith<_$LocationFormDataImpl> get copyWith =>
      __$$LocationFormDataImplCopyWithImpl<_$LocationFormDataImpl>(
          this, _$identity);
}

abstract class _LocationFormData implements LocationFormData {
  const factory _LocationFormData(
      {final String addressLine1,
      final String landmark,
      final String city,
      final String state,
      final String pincode,
      final String addressType,
      final bool isDefault,
      final double latitude,
      final double longitude,
      final Map<String, String> fieldErrors,
      final bool isValid}) = _$LocationFormDataImpl;

  @override
  String get addressLine1;
  @override
  String get landmark;
  @override
  String get city;
  @override
  String get state;
  @override
  String get pincode;
  @override
  String get addressType;
  @override
  bool get isDefault;
  @override
  double get latitude;
  @override
  double get longitude;
  @override
  Map<String, String> get fieldErrors;
  @override
  bool get isValid;

  /// Create a copy of LocationFormData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationFormDataImplCopyWith<_$LocationFormDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$LocationMapData {
  double get latitude => throw _privateConstructorUsedError;
  double get longitude => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;

  /// Create a copy of LocationMapData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationMapDataCopyWith<LocationMapData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationMapDataCopyWith<$Res> {
  factory $LocationMapDataCopyWith(
          LocationMapData value, $Res Function(LocationMapData) then) =
      _$LocationMapDataCopyWithImpl<$Res, LocationMapData>;
  @useResult
  $Res call(
      {double latitude, double longitude, String address, bool isLoading});
}

/// @nodoc
class _$LocationMapDataCopyWithImpl<$Res, $Val extends LocationMapData>
    implements $LocationMapDataCopyWith<$Res> {
  _$LocationMapDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationMapData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? address = null,
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocationMapDataImplCopyWith<$Res>
    implements $LocationMapDataCopyWith<$Res> {
  factory _$$LocationMapDataImplCopyWith(_$LocationMapDataImpl value,
          $Res Function(_$LocationMapDataImpl) then) =
      __$$LocationMapDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double latitude, double longitude, String address, bool isLoading});
}

/// @nodoc
class __$$LocationMapDataImplCopyWithImpl<$Res>
    extends _$LocationMapDataCopyWithImpl<$Res, _$LocationMapDataImpl>
    implements _$$LocationMapDataImplCopyWith<$Res> {
  __$$LocationMapDataImplCopyWithImpl(
      _$LocationMapDataImpl _value, $Res Function(_$LocationMapDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationMapData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? address = null,
    Object? isLoading = null,
  }) {
    return _then(_$LocationMapDataImpl(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$LocationMapDataImpl implements _LocationMapData {
  const _$LocationMapDataImpl(
      {this.latitude = 0.0,
      this.longitude = 0.0,
      this.address = '',
      this.isLoading = false});

  @override
  @JsonKey()
  final double latitude;
  @override
  @JsonKey()
  final double longitude;
  @override
  @JsonKey()
  final String address;
  @override
  @JsonKey()
  final bool isLoading;

  @override
  String toString() {
    return 'LocationMapData(latitude: $latitude, longitude: $longitude, address: $address, isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationMapDataImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, latitude, longitude, address, isLoading);

  /// Create a copy of LocationMapData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationMapDataImplCopyWith<_$LocationMapDataImpl> get copyWith =>
      __$$LocationMapDataImplCopyWithImpl<_$LocationMapDataImpl>(
          this, _$identity);
}

abstract class _LocationMapData implements LocationMapData {
  const factory _LocationMapData(
      {final double latitude,
      final double longitude,
      final String address,
      final bool isLoading}) = _$LocationMapDataImpl;

  @override
  double get latitude;
  @override
  double get longitude;
  @override
  String get address;
  @override
  bool get isLoading;

  /// Create a copy of LocationMapData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationMapDataImplCopyWith<_$LocationMapDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$LocationState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationStateCopyWith<$Res> {
  factory $LocationStateCopyWith(
          LocationState value, $Res Function(LocationState) then) =
      _$LocationStateCopyWithImpl<$Res, LocationState>;
}

/// @nodoc
class _$LocationStateCopyWithImpl<$Res, $Val extends LocationState>
    implements $LocationStateCopyWith<$Res> {
  _$LocationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'LocationState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements LocationState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'LocationState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements LocationState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AddressModel address});
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
  }) {
    return _then(_$LoadedImpl(
      null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as AddressModel,
    ));
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl(this.address);

  @override
  final AddressModel address;

  @override
  String toString() {
    return 'LocationState.loaded(address: $address)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, address);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return loaded(address);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return loaded?.call(address);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(address);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements LocationState {
  const factory _Loaded(final AddressModel address) = _$LoadedImpl;

  AddressModel get address;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'LocationState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements LocationState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddressesLoadedImplCopyWith<$Res> {
  factory _$$AddressesLoadedImplCopyWith(_$AddressesLoadedImpl value,
          $Res Function(_$AddressesLoadedImpl) then) =
      __$$AddressesLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<AddressModel> addresses, String? defaultAddressId});
}

/// @nodoc
class __$$AddressesLoadedImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$AddressesLoadedImpl>
    implements _$$AddressesLoadedImplCopyWith<$Res> {
  __$$AddressesLoadedImplCopyWithImpl(
      _$AddressesLoadedImpl _value, $Res Function(_$AddressesLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addresses = null,
    Object? defaultAddressId = freezed,
  }) {
    return _then(_$AddressesLoadedImpl(
      null == addresses
          ? _value._addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<AddressModel>,
      freezed == defaultAddressId
          ? _value.defaultAddressId
          : defaultAddressId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AddressesLoadedImpl implements _AddressesLoaded {
  const _$AddressesLoadedImpl(
      final List<AddressModel> addresses, this.defaultAddressId)
      : _addresses = addresses;

  final List<AddressModel> _addresses;
  @override
  List<AddressModel> get addresses {
    if (_addresses is EqualUnmodifiableListView) return _addresses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_addresses);
  }

  @override
  final String? defaultAddressId;

  @override
  String toString() {
    return 'LocationState.addressesLoaded(addresses: $addresses, defaultAddressId: $defaultAddressId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddressesLoadedImpl &&
            const DeepCollectionEquality()
                .equals(other._addresses, _addresses) &&
            (identical(other.defaultAddressId, defaultAddressId) ||
                other.defaultAddressId == defaultAddressId));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_addresses), defaultAddressId);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddressesLoadedImplCopyWith<_$AddressesLoadedImpl> get copyWith =>
      __$$AddressesLoadedImplCopyWithImpl<_$AddressesLoadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return addressesLoaded(addresses, defaultAddressId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return addressesLoaded?.call(addresses, defaultAddressId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (addressesLoaded != null) {
      return addressesLoaded(addresses, defaultAddressId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return addressesLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return addressesLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (addressesLoaded != null) {
      return addressesLoaded(this);
    }
    return orElse();
  }
}

abstract class _AddressesLoaded implements LocationState {
  const factory _AddressesLoaded(
          final List<AddressModel> addresses, final String? defaultAddressId) =
      _$AddressesLoadedImpl;

  List<AddressModel> get addresses;
  String? get defaultAddressId;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddressesLoadedImplCopyWith<_$AddressesLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddressSavedImplCopyWith<$Res> {
  factory _$$AddressSavedImplCopyWith(
          _$AddressSavedImpl value, $Res Function(_$AddressSavedImpl) then) =
      __$$AddressSavedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AddressModel address});
}

/// @nodoc
class __$$AddressSavedImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$AddressSavedImpl>
    implements _$$AddressSavedImplCopyWith<$Res> {
  __$$AddressSavedImplCopyWithImpl(
      _$AddressSavedImpl _value, $Res Function(_$AddressSavedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
  }) {
    return _then(_$AddressSavedImpl(
      null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as AddressModel,
    ));
  }
}

/// @nodoc

class _$AddressSavedImpl implements _AddressSaved {
  const _$AddressSavedImpl(this.address);

  @override
  final AddressModel address;

  @override
  String toString() {
    return 'LocationState.addressSaved(address: $address)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddressSavedImpl &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, address);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddressSavedImplCopyWith<_$AddressSavedImpl> get copyWith =>
      __$$AddressSavedImplCopyWithImpl<_$AddressSavedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return addressSaved(address);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return addressSaved?.call(address);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (addressSaved != null) {
      return addressSaved(address);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return addressSaved(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return addressSaved?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (addressSaved != null) {
      return addressSaved(this);
    }
    return orElse();
  }
}

abstract class _AddressSaved implements LocationState {
  const factory _AddressSaved(final AddressModel address) = _$AddressSavedImpl;

  AddressModel get address;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddressSavedImplCopyWith<_$AddressSavedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddressDeletedImplCopyWith<$Res> {
  factory _$$AddressDeletedImplCopyWith(_$AddressDeletedImpl value,
          $Res Function(_$AddressDeletedImpl) then) =
      __$$AddressDeletedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String addressId});
}

/// @nodoc
class __$$AddressDeletedImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$AddressDeletedImpl>
    implements _$$AddressDeletedImplCopyWith<$Res> {
  __$$AddressDeletedImplCopyWithImpl(
      _$AddressDeletedImpl _value, $Res Function(_$AddressDeletedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressId = null,
  }) {
    return _then(_$AddressDeletedImpl(
      null == addressId
          ? _value.addressId
          : addressId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AddressDeletedImpl implements _AddressDeleted {
  const _$AddressDeletedImpl(this.addressId);

  @override
  final String addressId;

  @override
  String toString() {
    return 'LocationState.addressDeleted(addressId: $addressId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddressDeletedImpl &&
            (identical(other.addressId, addressId) ||
                other.addressId == addressId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, addressId);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddressDeletedImplCopyWith<_$AddressDeletedImpl> get copyWith =>
      __$$AddressDeletedImplCopyWithImpl<_$AddressDeletedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return addressDeleted(addressId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return addressDeleted?.call(addressId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (addressDeleted != null) {
      return addressDeleted(addressId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return addressDeleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return addressDeleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (addressDeleted != null) {
      return addressDeleted(this);
    }
    return orElse();
  }
}

abstract class _AddressDeleted implements LocationState {
  const factory _AddressDeleted(final String addressId) = _$AddressDeletedImpl;

  String get addressId;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddressDeletedImplCopyWith<_$AddressDeletedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DefaultAddressSetImplCopyWith<$Res> {
  factory _$$DefaultAddressSetImplCopyWith(_$DefaultAddressSetImpl value,
          $Res Function(_$DefaultAddressSetImpl) then) =
      __$$DefaultAddressSetImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String addressId});
}

/// @nodoc
class __$$DefaultAddressSetImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$DefaultAddressSetImpl>
    implements _$$DefaultAddressSetImplCopyWith<$Res> {
  __$$DefaultAddressSetImplCopyWithImpl(_$DefaultAddressSetImpl _value,
      $Res Function(_$DefaultAddressSetImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressId = null,
  }) {
    return _then(_$DefaultAddressSetImpl(
      null == addressId
          ? _value.addressId
          : addressId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DefaultAddressSetImpl implements _DefaultAddressSet {
  const _$DefaultAddressSetImpl(this.addressId);

  @override
  final String addressId;

  @override
  String toString() {
    return 'LocationState.defaultAddressSet(addressId: $addressId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DefaultAddressSetImpl &&
            (identical(other.addressId, addressId) ||
                other.addressId == addressId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, addressId);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DefaultAddressSetImplCopyWith<_$DefaultAddressSetImpl> get copyWith =>
      __$$DefaultAddressSetImplCopyWithImpl<_$DefaultAddressSetImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return defaultAddressSet(addressId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return defaultAddressSet?.call(addressId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (defaultAddressSet != null) {
      return defaultAddressSet(addressId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return defaultAddressSet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return defaultAddressSet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (defaultAddressSet != null) {
      return defaultAddressSet(this);
    }
    return orElse();
  }
}

abstract class _DefaultAddressSet implements LocationState {
  const factory _DefaultAddressSet(final String addressId) =
      _$DefaultAddressSetImpl;

  String get addressId;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DefaultAddressSetImplCopyWith<_$DefaultAddressSetImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FormStateImplCopyWith<$Res> {
  factory _$$FormStateImplCopyWith(
          _$FormStateImpl value, $Res Function(_$FormStateImpl) then) =
      __$$FormStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LocationFormData formData});

  $LocationFormDataCopyWith<$Res> get formData;
}

/// @nodoc
class __$$FormStateImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$FormStateImpl>
    implements _$$FormStateImplCopyWith<$Res> {
  __$$FormStateImplCopyWithImpl(
      _$FormStateImpl _value, $Res Function(_$FormStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? formData = null,
  }) {
    return _then(_$FormStateImpl(
      null == formData
          ? _value.formData
          : formData // ignore: cast_nullable_to_non_nullable
              as LocationFormData,
    ));
  }

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationFormDataCopyWith<$Res> get formData {
    return $LocationFormDataCopyWith<$Res>(_value.formData, (value) {
      return _then(_value.copyWith(formData: value));
    });
  }
}

/// @nodoc

class _$FormStateImpl implements _FormState {
  const _$FormStateImpl(this.formData);

  @override
  final LocationFormData formData;

  @override
  String toString() {
    return 'LocationState.formState(formData: $formData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FormStateImpl &&
            (identical(other.formData, formData) ||
                other.formData == formData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, formData);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FormStateImplCopyWith<_$FormStateImpl> get copyWith =>
      __$$FormStateImplCopyWithImpl<_$FormStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return formState(formData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return formState?.call(formData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (formState != null) {
      return formState(formData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return formState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return formState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (formState != null) {
      return formState(this);
    }
    return orElse();
  }
}

abstract class _FormState implements LocationState {
  const factory _FormState(final LocationFormData formData) = _$FormStateImpl;

  LocationFormData get formData;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FormStateImplCopyWith<_$FormStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FormErrorImplCopyWith<$Res> {
  factory _$$FormErrorImplCopyWith(
          _$FormErrorImpl value, $Res Function(_$FormErrorImpl) then) =
      __$$FormErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<String, String> errors});
}

/// @nodoc
class __$$FormErrorImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$FormErrorImpl>
    implements _$$FormErrorImplCopyWith<$Res> {
  __$$FormErrorImplCopyWithImpl(
      _$FormErrorImpl _value, $Res Function(_$FormErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errors = null,
  }) {
    return _then(_$FormErrorImpl(
      null == errors
          ? _value._errors
          : errors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
    ));
  }
}

/// @nodoc

class _$FormErrorImpl implements _FormError {
  const _$FormErrorImpl(final Map<String, String> errors) : _errors = errors;

  final Map<String, String> _errors;
  @override
  Map<String, String> get errors {
    if (_errors is EqualUnmodifiableMapView) return _errors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_errors);
  }

  @override
  String toString() {
    return 'LocationState.formError(errors: $errors)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FormErrorImpl &&
            const DeepCollectionEquality().equals(other._errors, _errors));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_errors));

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FormErrorImplCopyWith<_$FormErrorImpl> get copyWith =>
      __$$FormErrorImplCopyWithImpl<_$FormErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return formError(errors);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return formError?.call(errors);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (formError != null) {
      return formError(errors);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return formError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return formError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (formError != null) {
      return formError(this);
    }
    return orElse();
  }
}

abstract class _FormError implements LocationState {
  const factory _FormError(final Map<String, String> errors) = _$FormErrorImpl;

  Map<String, String> get errors;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FormErrorImplCopyWith<_$FormErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MapStateImplCopyWith<$Res> {
  factory _$$MapStateImplCopyWith(
          _$MapStateImpl value, $Res Function(_$MapStateImpl) then) =
      __$$MapStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LocationMapData mapData});

  $LocationMapDataCopyWith<$Res> get mapData;
}

/// @nodoc
class __$$MapStateImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$MapStateImpl>
    implements _$$MapStateImplCopyWith<$Res> {
  __$$MapStateImplCopyWithImpl(
      _$MapStateImpl _value, $Res Function(_$MapStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mapData = null,
  }) {
    return _then(_$MapStateImpl(
      null == mapData
          ? _value.mapData
          : mapData // ignore: cast_nullable_to_non_nullable
              as LocationMapData,
    ));
  }

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationMapDataCopyWith<$Res> get mapData {
    return $LocationMapDataCopyWith<$Res>(_value.mapData, (value) {
      return _then(_value.copyWith(mapData: value));
    });
  }
}

/// @nodoc

class _$MapStateImpl implements _MapState {
  const _$MapStateImpl(this.mapData);

  @override
  final LocationMapData mapData;

  @override
  String toString() {
    return 'LocationState.mapState(mapData: $mapData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MapStateImpl &&
            (identical(other.mapData, mapData) || other.mapData == mapData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mapData);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MapStateImplCopyWith<_$MapStateImpl> get copyWith =>
      __$$MapStateImplCopyWithImpl<_$MapStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return mapState(mapData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return mapState?.call(mapData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (mapState != null) {
      return mapState(mapData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return mapState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return mapState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (mapState != null) {
      return mapState(this);
    }
    return orElse();
  }
}

abstract class _MapState implements LocationState {
  const factory _MapState(final LocationMapData mapData) = _$MapStateImpl;

  LocationMapData get mapData;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MapStateImplCopyWith<_$MapStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LocationDetectedImplCopyWith<$Res> {
  factory _$$LocationDetectedImplCopyWith(_$LocationDetectedImpl value,
          $Res Function(_$LocationDetectedImpl) then) =
      __$$LocationDetectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({double latitude, double longitude, String address});
}

/// @nodoc
class __$$LocationDetectedImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$LocationDetectedImpl>
    implements _$$LocationDetectedImplCopyWith<$Res> {
  __$$LocationDetectedImplCopyWithImpl(_$LocationDetectedImpl _value,
      $Res Function(_$LocationDetectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? address = null,
  }) {
    return _then(_$LocationDetectedImpl(
      null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$LocationDetectedImpl implements _LocationDetected {
  const _$LocationDetectedImpl(this.latitude, this.longitude, this.address);

  @override
  final double latitude;
  @override
  final double longitude;
  @override
  final String address;

  @override
  String toString() {
    return 'LocationState.locationDetected(latitude: $latitude, longitude: $longitude, address: $address)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationDetectedImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, latitude, longitude, address);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationDetectedImplCopyWith<_$LocationDetectedImpl> get copyWith =>
      __$$LocationDetectedImplCopyWithImpl<_$LocationDetectedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return locationDetected(latitude, longitude, address);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return locationDetected?.call(latitude, longitude, address);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (locationDetected != null) {
      return locationDetected(latitude, longitude, address);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return locationDetected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return locationDetected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (locationDetected != null) {
      return locationDetected(this);
    }
    return orElse();
  }
}

abstract class _LocationDetected implements LocationState {
  const factory _LocationDetected(
          final double latitude, final double longitude, final String address) =
      _$LocationDetectedImpl;

  double get latitude;
  double get longitude;
  String get address;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationDetectedImplCopyWith<_$LocationDetectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchResultsImplCopyWith<$Res> {
  factory _$$SearchResultsImplCopyWith(
          _$SearchResultsImpl value, $Res Function(_$SearchResultsImpl) then) =
      __$$SearchResultsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<AddressModel> results});
}

/// @nodoc
class __$$SearchResultsImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$SearchResultsImpl>
    implements _$$SearchResultsImplCopyWith<$Res> {
  __$$SearchResultsImplCopyWithImpl(
      _$SearchResultsImpl _value, $Res Function(_$SearchResultsImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? results = null,
  }) {
    return _then(_$SearchResultsImpl(
      null == results
          ? _value._results
          : results // ignore: cast_nullable_to_non_nullable
              as List<AddressModel>,
    ));
  }
}

/// @nodoc

class _$SearchResultsImpl implements _SearchResults {
  const _$SearchResultsImpl(final List<AddressModel> results)
      : _results = results;

  final List<AddressModel> _results;
  @override
  List<AddressModel> get results {
    if (_results is EqualUnmodifiableListView) return _results;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_results);
  }

  @override
  String toString() {
    return 'LocationState.searchResults(results: $results)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchResultsImpl &&
            const DeepCollectionEquality().equals(other._results, _results));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_results));

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchResultsImplCopyWith<_$SearchResultsImpl> get copyWith =>
      __$$SearchResultsImplCopyWithImpl<_$SearchResultsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(String message) error,
    required TResult Function(
            List<AddressModel> addresses, String? defaultAddressId)
        addressesLoaded,
    required TResult Function(AddressModel address) addressSaved,
    required TResult Function(String addressId) addressDeleted,
    required TResult Function(String addressId) defaultAddressSet,
    required TResult Function(LocationFormData formData) formState,
    required TResult Function(Map<String, String> errors) formError,
    required TResult Function(LocationMapData mapData) mapState,
    required TResult Function(double latitude, double longitude, String address)
        locationDetected,
    required TResult Function(List<AddressModel> results) searchResults,
  }) {
    return searchResults(results);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(String message)? error,
    TResult? Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult? Function(AddressModel address)? addressSaved,
    TResult? Function(String addressId)? addressDeleted,
    TResult? Function(String addressId)? defaultAddressSet,
    TResult? Function(LocationFormData formData)? formState,
    TResult? Function(Map<String, String> errors)? formError,
    TResult? Function(LocationMapData mapData)? mapState,
    TResult? Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult? Function(List<AddressModel> results)? searchResults,
  }) {
    return searchResults?.call(results);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(String message)? error,
    TResult Function(List<AddressModel> addresses, String? defaultAddressId)?
        addressesLoaded,
    TResult Function(AddressModel address)? addressSaved,
    TResult Function(String addressId)? addressDeleted,
    TResult Function(String addressId)? defaultAddressSet,
    TResult Function(LocationFormData formData)? formState,
    TResult Function(Map<String, String> errors)? formError,
    TResult Function(LocationMapData mapData)? mapState,
    TResult Function(double latitude, double longitude, String address)?
        locationDetected,
    TResult Function(List<AddressModel> results)? searchResults,
    required TResult orElse(),
  }) {
    if (searchResults != null) {
      return searchResults(results);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_AddressesLoaded value) addressesLoaded,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_AddressDeleted value) addressDeleted,
    required TResult Function(_DefaultAddressSet value) defaultAddressSet,
    required TResult Function(_FormState value) formState,
    required TResult Function(_FormError value) formError,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_LocationDetected value) locationDetected,
    required TResult Function(_SearchResults value) searchResults,
  }) {
    return searchResults(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_AddressesLoaded value)? addressesLoaded,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_AddressDeleted value)? addressDeleted,
    TResult? Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult? Function(_FormState value)? formState,
    TResult? Function(_FormError value)? formError,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_LocationDetected value)? locationDetected,
    TResult? Function(_SearchResults value)? searchResults,
  }) {
    return searchResults?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_AddressesLoaded value)? addressesLoaded,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_AddressDeleted value)? addressDeleted,
    TResult Function(_DefaultAddressSet value)? defaultAddressSet,
    TResult Function(_FormState value)? formState,
    TResult Function(_FormError value)? formError,
    TResult Function(_MapState value)? mapState,
    TResult Function(_LocationDetected value)? locationDetected,
    TResult Function(_SearchResults value)? searchResults,
    required TResult orElse(),
  }) {
    if (searchResults != null) {
      return searchResults(this);
    }
    return orElse();
  }
}

abstract class _SearchResults implements LocationState {
  const factory _SearchResults(final List<AddressModel> results) =
      _$SearchResultsImpl;

  List<AddressModel> get results;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchResultsImplCopyWith<_$SearchResultsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
