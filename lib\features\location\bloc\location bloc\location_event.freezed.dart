// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LocationEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationEventCopyWith<$Res> {
  factory $LocationEventCopyWith(
          LocationEvent value, $Res Function(LocationEvent) then) =
      _$LocationEventCopyWithImpl<$Res, LocationEvent>;
}

/// @nodoc
class _$LocationEventCopyWithImpl<$Res, $Val extends LocationEvent>
    implements $LocationEventCopyWith<$Res> {
  _$LocationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'LocationEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements LocationEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
abstract class _$$RefreshLocationImplCopyWith<$Res> {
  factory _$$RefreshLocationImplCopyWith(_$RefreshLocationImpl value,
          $Res Function(_$RefreshLocationImpl) then) =
      __$$RefreshLocationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RefreshLocationImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$RefreshLocationImpl>
    implements _$$RefreshLocationImplCopyWith<$Res> {
  __$$RefreshLocationImplCopyWithImpl(
      _$RefreshLocationImpl _value, $Res Function(_$RefreshLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RefreshLocationImpl implements _RefreshLocation {
  const _$RefreshLocationImpl();

  @override
  String toString() {
    return 'LocationEvent.refreshLocation()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RefreshLocationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return refreshLocation();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return refreshLocation?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (refreshLocation != null) {
      return refreshLocation();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return refreshLocation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return refreshLocation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (refreshLocation != null) {
      return refreshLocation(this);
    }
    return orElse();
  }
}

abstract class _RefreshLocation implements LocationEvent {
  const factory _RefreshLocation() = _$RefreshLocationImpl;
}

/// @nodoc
abstract class _$$LoadAddressesImplCopyWith<$Res> {
  factory _$$LoadAddressesImplCopyWith(
          _$LoadAddressesImpl value, $Res Function(_$LoadAddressesImpl) then) =
      __$$LoadAddressesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadAddressesImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$LoadAddressesImpl>
    implements _$$LoadAddressesImplCopyWith<$Res> {
  __$$LoadAddressesImplCopyWithImpl(
      _$LoadAddressesImpl _value, $Res Function(_$LoadAddressesImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadAddressesImpl implements _LoadAddresses {
  const _$LoadAddressesImpl();

  @override
  String toString() {
    return 'LocationEvent.loadAddresses()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadAddressesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return loadAddresses();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return loadAddresses?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (loadAddresses != null) {
      return loadAddresses();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return loadAddresses(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return loadAddresses?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (loadAddresses != null) {
      return loadAddresses(this);
    }
    return orElse();
  }
}

abstract class _LoadAddresses implements LocationEvent {
  const factory _LoadAddresses() = _$LoadAddressesImpl;
}

/// @nodoc
abstract class _$$AddAddressImplCopyWith<$Res> {
  factory _$$AddAddressImplCopyWith(
          _$AddAddressImpl value, $Res Function(_$AddAddressImpl) then) =
      __$$AddAddressImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AddressModel address});
}

/// @nodoc
class __$$AddAddressImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$AddAddressImpl>
    implements _$$AddAddressImplCopyWith<$Res> {
  __$$AddAddressImplCopyWithImpl(
      _$AddAddressImpl _value, $Res Function(_$AddAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
  }) {
    return _then(_$AddAddressImpl(
      null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as AddressModel,
    ));
  }
}

/// @nodoc

class _$AddAddressImpl implements _AddAddress {
  const _$AddAddressImpl(this.address);

  @override
  final AddressModel address;

  @override
  String toString() {
    return 'LocationEvent.addAddress(address: $address)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddAddressImpl &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, address);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddAddressImplCopyWith<_$AddAddressImpl> get copyWith =>
      __$$AddAddressImplCopyWithImpl<_$AddAddressImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return addAddress(address);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return addAddress?.call(address);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (addAddress != null) {
      return addAddress(address);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return addAddress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return addAddress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (addAddress != null) {
      return addAddress(this);
    }
    return orElse();
  }
}

abstract class _AddAddress implements LocationEvent {
  const factory _AddAddress(final AddressModel address) = _$AddAddressImpl;

  AddressModel get address;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddAddressImplCopyWith<_$AddAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EditAddressImplCopyWith<$Res> {
  factory _$$EditAddressImplCopyWith(
          _$EditAddressImpl value, $Res Function(_$EditAddressImpl) then) =
      __$$EditAddressImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AddressModel address});
}

/// @nodoc
class __$$EditAddressImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$EditAddressImpl>
    implements _$$EditAddressImplCopyWith<$Res> {
  __$$EditAddressImplCopyWithImpl(
      _$EditAddressImpl _value, $Res Function(_$EditAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
  }) {
    return _then(_$EditAddressImpl(
      null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as AddressModel,
    ));
  }
}

/// @nodoc

class _$EditAddressImpl implements _EditAddress {
  const _$EditAddressImpl(this.address);

  @override
  final AddressModel address;

  @override
  String toString() {
    return 'LocationEvent.editAddress(address: $address)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditAddressImpl &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, address);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditAddressImplCopyWith<_$EditAddressImpl> get copyWith =>
      __$$EditAddressImplCopyWithImpl<_$EditAddressImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return editAddress(address);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return editAddress?.call(address);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (editAddress != null) {
      return editAddress(address);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return editAddress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return editAddress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (editAddress != null) {
      return editAddress(this);
    }
    return orElse();
  }
}

abstract class _EditAddress implements LocationEvent {
  const factory _EditAddress(final AddressModel address) = _$EditAddressImpl;

  AddressModel get address;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditAddressImplCopyWith<_$EditAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteAddressImplCopyWith<$Res> {
  factory _$$DeleteAddressImplCopyWith(
          _$DeleteAddressImpl value, $Res Function(_$DeleteAddressImpl) then) =
      __$$DeleteAddressImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String addressId});
}

/// @nodoc
class __$$DeleteAddressImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$DeleteAddressImpl>
    implements _$$DeleteAddressImplCopyWith<$Res> {
  __$$DeleteAddressImplCopyWithImpl(
      _$DeleteAddressImpl _value, $Res Function(_$DeleteAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressId = null,
  }) {
    return _then(_$DeleteAddressImpl(
      null == addressId
          ? _value.addressId
          : addressId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteAddressImpl implements _DeleteAddress {
  const _$DeleteAddressImpl(this.addressId);

  @override
  final String addressId;

  @override
  String toString() {
    return 'LocationEvent.deleteAddress(addressId: $addressId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteAddressImpl &&
            (identical(other.addressId, addressId) ||
                other.addressId == addressId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, addressId);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteAddressImplCopyWith<_$DeleteAddressImpl> get copyWith =>
      __$$DeleteAddressImplCopyWithImpl<_$DeleteAddressImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return deleteAddress(addressId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return deleteAddress?.call(addressId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (deleteAddress != null) {
      return deleteAddress(addressId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return deleteAddress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return deleteAddress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (deleteAddress != null) {
      return deleteAddress(this);
    }
    return orElse();
  }
}

abstract class _DeleteAddress implements LocationEvent {
  const factory _DeleteAddress(final String addressId) = _$DeleteAddressImpl;

  String get addressId;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteAddressImplCopyWith<_$DeleteAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SetDefaultAddressImplCopyWith<$Res> {
  factory _$$SetDefaultAddressImplCopyWith(_$SetDefaultAddressImpl value,
          $Res Function(_$SetDefaultAddressImpl) then) =
      __$$SetDefaultAddressImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String addressId});
}

/// @nodoc
class __$$SetDefaultAddressImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$SetDefaultAddressImpl>
    implements _$$SetDefaultAddressImplCopyWith<$Res> {
  __$$SetDefaultAddressImplCopyWithImpl(_$SetDefaultAddressImpl _value,
      $Res Function(_$SetDefaultAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressId = null,
  }) {
    return _then(_$SetDefaultAddressImpl(
      null == addressId
          ? _value.addressId
          : addressId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SetDefaultAddressImpl implements _SetDefaultAddress {
  const _$SetDefaultAddressImpl(this.addressId);

  @override
  final String addressId;

  @override
  String toString() {
    return 'LocationEvent.setDefaultAddress(addressId: $addressId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetDefaultAddressImpl &&
            (identical(other.addressId, addressId) ||
                other.addressId == addressId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, addressId);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SetDefaultAddressImplCopyWith<_$SetDefaultAddressImpl> get copyWith =>
      __$$SetDefaultAddressImplCopyWithImpl<_$SetDefaultAddressImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return setDefaultAddress(addressId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return setDefaultAddress?.call(addressId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (setDefaultAddress != null) {
      return setDefaultAddress(addressId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return setDefaultAddress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return setDefaultAddress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (setDefaultAddress != null) {
      return setDefaultAddress(this);
    }
    return orElse();
  }
}

abstract class _SetDefaultAddress implements LocationEvent {
  const factory _SetDefaultAddress(final String addressId) =
      _$SetDefaultAddressImpl;

  String get addressId;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SetDefaultAddressImplCopyWith<_$SetDefaultAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetCurrentLocationImplCopyWith<$Res> {
  factory _$$GetCurrentLocationImplCopyWith(_$GetCurrentLocationImpl value,
          $Res Function(_$GetCurrentLocationImpl) then) =
      __$$GetCurrentLocationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetCurrentLocationImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$GetCurrentLocationImpl>
    implements _$$GetCurrentLocationImplCopyWith<$Res> {
  __$$GetCurrentLocationImplCopyWithImpl(_$GetCurrentLocationImpl _value,
      $Res Function(_$GetCurrentLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetCurrentLocationImpl implements _GetCurrentLocation {
  const _$GetCurrentLocationImpl();

  @override
  String toString() {
    return 'LocationEvent.getCurrentLocation()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetCurrentLocationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return getCurrentLocation();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return getCurrentLocation?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (getCurrentLocation != null) {
      return getCurrentLocation();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return getCurrentLocation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return getCurrentLocation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (getCurrentLocation != null) {
      return getCurrentLocation(this);
    }
    return orElse();
  }
}

abstract class _GetCurrentLocation implements LocationEvent {
  const factory _GetCurrentLocation() = _$GetCurrentLocationImpl;
}

/// @nodoc
abstract class _$$GetAddressFromCoordinatesImplCopyWith<$Res> {
  factory _$$GetAddressFromCoordinatesImplCopyWith(
          _$GetAddressFromCoordinatesImpl value,
          $Res Function(_$GetAddressFromCoordinatesImpl) then) =
      __$$GetAddressFromCoordinatesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({double latitude, double longitude});
}

/// @nodoc
class __$$GetAddressFromCoordinatesImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$GetAddressFromCoordinatesImpl>
    implements _$$GetAddressFromCoordinatesImplCopyWith<$Res> {
  __$$GetAddressFromCoordinatesImplCopyWithImpl(
      _$GetAddressFromCoordinatesImpl _value,
      $Res Function(_$GetAddressFromCoordinatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
  }) {
    return _then(_$GetAddressFromCoordinatesImpl(
      null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$GetAddressFromCoordinatesImpl implements _GetAddressFromCoordinates {
  const _$GetAddressFromCoordinatesImpl(this.latitude, this.longitude);

  @override
  final double latitude;
  @override
  final double longitude;

  @override
  String toString() {
    return 'LocationEvent.getAddressFromCoordinates(latitude: $latitude, longitude: $longitude)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetAddressFromCoordinatesImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude));
  }

  @override
  int get hashCode => Object.hash(runtimeType, latitude, longitude);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetAddressFromCoordinatesImplCopyWith<_$GetAddressFromCoordinatesImpl>
      get copyWith => __$$GetAddressFromCoordinatesImplCopyWithImpl<
          _$GetAddressFromCoordinatesImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return getAddressFromCoordinates(latitude, longitude);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return getAddressFromCoordinates?.call(latitude, longitude);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (getAddressFromCoordinates != null) {
      return getAddressFromCoordinates(latitude, longitude);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return getAddressFromCoordinates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return getAddressFromCoordinates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (getAddressFromCoordinates != null) {
      return getAddressFromCoordinates(this);
    }
    return orElse();
  }
}

abstract class _GetAddressFromCoordinates implements LocationEvent {
  const factory _GetAddressFromCoordinates(
          final double latitude, final double longitude) =
      _$GetAddressFromCoordinatesImpl;

  double get latitude;
  double get longitude;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetAddressFromCoordinatesImplCopyWith<_$GetAddressFromCoordinatesImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchLocationImplCopyWith<$Res> {
  factory _$$SearchLocationImplCopyWith(_$SearchLocationImpl value,
          $Res Function(_$SearchLocationImpl) then) =
      __$$SearchLocationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$$SearchLocationImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$SearchLocationImpl>
    implements _$$SearchLocationImplCopyWith<$Res> {
  __$$SearchLocationImplCopyWithImpl(
      _$SearchLocationImpl _value, $Res Function(_$SearchLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
  }) {
    return _then(_$SearchLocationImpl(
      null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchLocationImpl implements _SearchLocation {
  const _$SearchLocationImpl(this.query);

  @override
  final String query;

  @override
  String toString() {
    return 'LocationEvent.searchLocation(query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchLocationImpl &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchLocationImplCopyWith<_$SearchLocationImpl> get copyWith =>
      __$$SearchLocationImplCopyWithImpl<_$SearchLocationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return searchLocation(query);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return searchLocation?.call(query);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (searchLocation != null) {
      return searchLocation(query);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return searchLocation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return searchLocation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (searchLocation != null) {
      return searchLocation(this);
    }
    return orElse();
  }
}

abstract class _SearchLocation implements LocationEvent {
  const factory _SearchLocation(final String query) = _$SearchLocationImpl;

  String get query;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchLocationImplCopyWith<_$SearchLocationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectLocationOnMapImplCopyWith<$Res> {
  factory _$$SelectLocationOnMapImplCopyWith(_$SelectLocationOnMapImpl value,
          $Res Function(_$SelectLocationOnMapImpl) then) =
      __$$SelectLocationOnMapImplCopyWithImpl<$Res>;
  @useResult
  $Res call({double latitude, double longitude});
}

/// @nodoc
class __$$SelectLocationOnMapImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$SelectLocationOnMapImpl>
    implements _$$SelectLocationOnMapImplCopyWith<$Res> {
  __$$SelectLocationOnMapImplCopyWithImpl(_$SelectLocationOnMapImpl _value,
      $Res Function(_$SelectLocationOnMapImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
  }) {
    return _then(_$SelectLocationOnMapImpl(
      null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$SelectLocationOnMapImpl implements _SelectLocationOnMap {
  const _$SelectLocationOnMapImpl(this.latitude, this.longitude);

  @override
  final double latitude;
  @override
  final double longitude;

  @override
  String toString() {
    return 'LocationEvent.selectLocationOnMap(latitude: $latitude, longitude: $longitude)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectLocationOnMapImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude));
  }

  @override
  int get hashCode => Object.hash(runtimeType, latitude, longitude);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectLocationOnMapImplCopyWith<_$SelectLocationOnMapImpl> get copyWith =>
      __$$SelectLocationOnMapImplCopyWithImpl<_$SelectLocationOnMapImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return selectLocationOnMap(latitude, longitude);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return selectLocationOnMap?.call(latitude, longitude);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (selectLocationOnMap != null) {
      return selectLocationOnMap(latitude, longitude);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return selectLocationOnMap(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return selectLocationOnMap?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (selectLocationOnMap != null) {
      return selectLocationOnMap(this);
    }
    return orElse();
  }
}

abstract class _SelectLocationOnMap implements LocationEvent {
  const factory _SelectLocationOnMap(
          final double latitude, final double longitude) =
      _$SelectLocationOnMapImpl;

  double get latitude;
  double get longitude;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelectLocationOnMapImplCopyWith<_$SelectLocationOnMapImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateFormFieldImplCopyWith<$Res> {
  factory _$$UpdateFormFieldImplCopyWith(_$UpdateFormFieldImpl value,
          $Res Function(_$UpdateFormFieldImpl) then) =
      __$$UpdateFormFieldImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String fieldName, String value});
}

/// @nodoc
class __$$UpdateFormFieldImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$UpdateFormFieldImpl>
    implements _$$UpdateFormFieldImplCopyWith<$Res> {
  __$$UpdateFormFieldImplCopyWithImpl(
      _$UpdateFormFieldImpl _value, $Res Function(_$UpdateFormFieldImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fieldName = null,
    Object? value = null,
  }) {
    return _then(_$UpdateFormFieldImpl(
      null == fieldName
          ? _value.fieldName
          : fieldName // ignore: cast_nullable_to_non_nullable
              as String,
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateFormFieldImpl implements _UpdateFormField {
  const _$UpdateFormFieldImpl(this.fieldName, this.value);

  @override
  final String fieldName;
  @override
  final String value;

  @override
  String toString() {
    return 'LocationEvent.updateFormField(fieldName: $fieldName, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateFormFieldImpl &&
            (identical(other.fieldName, fieldName) ||
                other.fieldName == fieldName) &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fieldName, value);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateFormFieldImplCopyWith<_$UpdateFormFieldImpl> get copyWith =>
      __$$UpdateFormFieldImplCopyWithImpl<_$UpdateFormFieldImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return updateFormField(fieldName, value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return updateFormField?.call(fieldName, value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (updateFormField != null) {
      return updateFormField(fieldName, value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return updateFormField(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return updateFormField?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (updateFormField != null) {
      return updateFormField(this);
    }
    return orElse();
  }
}

abstract class _UpdateFormField implements LocationEvent {
  const factory _UpdateFormField(final String fieldName, final String value) =
      _$UpdateFormFieldImpl;

  String get fieldName;
  String get value;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateFormFieldImplCopyWith<_$UpdateFormFieldImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ValidateFormImplCopyWith<$Res> {
  factory _$$ValidateFormImplCopyWith(
          _$ValidateFormImpl value, $Res Function(_$ValidateFormImpl) then) =
      __$$ValidateFormImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ValidateFormImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$ValidateFormImpl>
    implements _$$ValidateFormImplCopyWith<$Res> {
  __$$ValidateFormImplCopyWithImpl(
      _$ValidateFormImpl _value, $Res Function(_$ValidateFormImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ValidateFormImpl implements _ValidateForm {
  const _$ValidateFormImpl();

  @override
  String toString() {
    return 'LocationEvent.validateForm()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ValidateFormImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return validateForm();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return validateForm?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (validateForm != null) {
      return validateForm();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return validateForm(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return validateForm?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (validateForm != null) {
      return validateForm(this);
    }
    return orElse();
  }
}

abstract class _ValidateForm implements LocationEvent {
  const factory _ValidateForm() = _$ValidateFormImpl;
}

/// @nodoc
abstract class _$$ResetFormImplCopyWith<$Res> {
  factory _$$ResetFormImplCopyWith(
          _$ResetFormImpl value, $Res Function(_$ResetFormImpl) then) =
      __$$ResetFormImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetFormImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$ResetFormImpl>
    implements _$$ResetFormImplCopyWith<$Res> {
  __$$ResetFormImplCopyWithImpl(
      _$ResetFormImpl _value, $Res Function(_$ResetFormImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResetFormImpl implements _ResetForm {
  const _$ResetFormImpl();

  @override
  String toString() {
    return 'LocationEvent.resetForm()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetFormImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return resetForm();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return resetForm?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (resetForm != null) {
      return resetForm();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return resetForm(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return resetForm?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (resetForm != null) {
      return resetForm(this);
    }
    return orElse();
  }
}

abstract class _ResetForm implements LocationEvent {
  const factory _ResetForm() = _$ResetFormImpl;
}

/// @nodoc
abstract class _$$SetAddressTypeImplCopyWith<$Res> {
  factory _$$SetAddressTypeImplCopyWith(_$SetAddressTypeImpl value,
          $Res Function(_$SetAddressTypeImpl) then) =
      __$$SetAddressTypeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String addressType});
}

/// @nodoc
class __$$SetAddressTypeImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$SetAddressTypeImpl>
    implements _$$SetAddressTypeImplCopyWith<$Res> {
  __$$SetAddressTypeImplCopyWithImpl(
      _$SetAddressTypeImpl _value, $Res Function(_$SetAddressTypeImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressType = null,
  }) {
    return _then(_$SetAddressTypeImpl(
      null == addressType
          ? _value.addressType
          : addressType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SetAddressTypeImpl implements _SetAddressType {
  const _$SetAddressTypeImpl(this.addressType);

  @override
  final String addressType;

  @override
  String toString() {
    return 'LocationEvent.setAddressType(addressType: $addressType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetAddressTypeImpl &&
            (identical(other.addressType, addressType) ||
                other.addressType == addressType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, addressType);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SetAddressTypeImplCopyWith<_$SetAddressTypeImpl> get copyWith =>
      __$$SetAddressTypeImplCopyWithImpl<_$SetAddressTypeImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return setAddressType(addressType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return setAddressType?.call(addressType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (setAddressType != null) {
      return setAddressType(addressType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return setAddressType(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return setAddressType?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (setAddressType != null) {
      return setAddressType(this);
    }
    return orElse();
  }
}

abstract class _SetAddressType implements LocationEvent {
  const factory _SetAddressType(final String addressType) =
      _$SetAddressTypeImpl;

  String get addressType;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SetAddressTypeImplCopyWith<_$SetAddressTypeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SetDefaultFlagImplCopyWith<$Res> {
  factory _$$SetDefaultFlagImplCopyWith(_$SetDefaultFlagImpl value,
          $Res Function(_$SetDefaultFlagImpl) then) =
      __$$SetDefaultFlagImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isDefault});
}

/// @nodoc
class __$$SetDefaultFlagImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$SetDefaultFlagImpl>
    implements _$$SetDefaultFlagImplCopyWith<$Res> {
  __$$SetDefaultFlagImplCopyWithImpl(
      _$SetDefaultFlagImpl _value, $Res Function(_$SetDefaultFlagImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isDefault = null,
  }) {
    return _then(_$SetDefaultFlagImpl(
      null == isDefault
          ? _value.isDefault
          : isDefault // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SetDefaultFlagImpl implements _SetDefaultFlag {
  const _$SetDefaultFlagImpl(this.isDefault);

  @override
  final bool isDefault;

  @override
  String toString() {
    return 'LocationEvent.setDefaultFlag(isDefault: $isDefault)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetDefaultFlagImpl &&
            (identical(other.isDefault, isDefault) ||
                other.isDefault == isDefault));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isDefault);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SetDefaultFlagImplCopyWith<_$SetDefaultFlagImpl> get copyWith =>
      __$$SetDefaultFlagImplCopyWithImpl<_$SetDefaultFlagImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function() loadAddresses,
    required TResult Function(AddressModel address) addAddress,
    required TResult Function(AddressModel address) editAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(String addressId) setDefaultAddress,
    required TResult Function() getCurrentLocation,
    required TResult Function(double latitude, double longitude)
        getAddressFromCoordinates,
    required TResult Function(String query) searchLocation,
    required TResult Function(double latitude, double longitude)
        selectLocationOnMap,
    required TResult Function(String fieldName, String value) updateFormField,
    required TResult Function() validateForm,
    required TResult Function() resetForm,
    required TResult Function(String addressType) setAddressType,
    required TResult Function(bool isDefault) setDefaultFlag,
  }) {
    return setDefaultFlag(isDefault);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function()? loadAddresses,
    TResult? Function(AddressModel address)? addAddress,
    TResult? Function(AddressModel address)? editAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(String addressId)? setDefaultAddress,
    TResult? Function()? getCurrentLocation,
    TResult? Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult? Function(String query)? searchLocation,
    TResult? Function(double latitude, double longitude)? selectLocationOnMap,
    TResult? Function(String fieldName, String value)? updateFormField,
    TResult? Function()? validateForm,
    TResult? Function()? resetForm,
    TResult? Function(String addressType)? setAddressType,
    TResult? Function(bool isDefault)? setDefaultFlag,
  }) {
    return setDefaultFlag?.call(isDefault);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function()? loadAddresses,
    TResult Function(AddressModel address)? addAddress,
    TResult Function(AddressModel address)? editAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(String addressId)? setDefaultAddress,
    TResult Function()? getCurrentLocation,
    TResult Function(double latitude, double longitude)?
        getAddressFromCoordinates,
    TResult Function(String query)? searchLocation,
    TResult Function(double latitude, double longitude)? selectLocationOnMap,
    TResult Function(String fieldName, String value)? updateFormField,
    TResult Function()? validateForm,
    TResult Function()? resetForm,
    TResult Function(String addressType)? setAddressType,
    TResult Function(bool isDefault)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (setDefaultFlag != null) {
      return setDefaultFlag(isDefault);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadAddresses value) loadAddresses,
    required TResult Function(_AddAddress value) addAddress,
    required TResult Function(_EditAddress value) editAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_SetDefaultAddress value) setDefaultAddress,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_GetAddressFromCoordinates value)
        getAddressFromCoordinates,
    required TResult Function(_SearchLocation value) searchLocation,
    required TResult Function(_SelectLocationOnMap value) selectLocationOnMap,
    required TResult Function(_UpdateFormField value) updateFormField,
    required TResult Function(_ValidateForm value) validateForm,
    required TResult Function(_ResetForm value) resetForm,
    required TResult Function(_SetAddressType value) setAddressType,
    required TResult Function(_SetDefaultFlag value) setDefaultFlag,
  }) {
    return setDefaultFlag(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadAddresses value)? loadAddresses,
    TResult? Function(_AddAddress value)? addAddress,
    TResult? Function(_EditAddress value)? editAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult? Function(_SearchLocation value)? searchLocation,
    TResult? Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult? Function(_UpdateFormField value)? updateFormField,
    TResult? Function(_ValidateForm value)? validateForm,
    TResult? Function(_ResetForm value)? resetForm,
    TResult? Function(_SetAddressType value)? setAddressType,
    TResult? Function(_SetDefaultFlag value)? setDefaultFlag,
  }) {
    return setDefaultFlag?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadAddresses value)? loadAddresses,
    TResult Function(_AddAddress value)? addAddress,
    TResult Function(_EditAddress value)? editAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_SetDefaultAddress value)? setDefaultAddress,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_GetAddressFromCoordinates value)?
        getAddressFromCoordinates,
    TResult Function(_SearchLocation value)? searchLocation,
    TResult Function(_SelectLocationOnMap value)? selectLocationOnMap,
    TResult Function(_UpdateFormField value)? updateFormField,
    TResult Function(_ValidateForm value)? validateForm,
    TResult Function(_ResetForm value)? resetForm,
    TResult Function(_SetAddressType value)? setAddressType,
    TResult Function(_SetDefaultFlag value)? setDefaultFlag,
    required TResult orElse(),
  }) {
    if (setDefaultFlag != null) {
      return setDefaultFlag(this);
    }
    return orElse();
  }
}

abstract class _SetDefaultFlag implements LocationEvent {
  const factory _SetDefaultFlag(final bool isDefault) = _$SetDefaultFlagImpl;

  bool get isDefault;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SetDefaultFlagImplCopyWith<_$SetDefaultFlagImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
