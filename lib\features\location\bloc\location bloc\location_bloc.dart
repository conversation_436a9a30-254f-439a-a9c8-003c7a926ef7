export 'location_event.dart';
export 'location_state.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../../../../data/models/adress_model.dart';
import '../../services/adress_services.dart';
import '../../services/locaion_services.dart';
import 'location_event.dart';
import 'location_state.dart';

class LocationBloc extends Bloc<LocationEvent, LocationState> {
  final AddressService addressService;
  final LocationService locationService;

  LocationBloc({
    required this.addressService,
    required this.locationService,
  }) : super(const LocationState.initial()) {
    on<LocationEvent>(
      (event, emit) => event.maybeWhen(
        // Core location events
        started: () => _onStarted(emit),
        refreshLocation: () => _onRefreshLocation(emit),

        // Address management events
        loadAddresses: () => _onLoadAddresses(emit),
        addAddress: (address) => _onAddAddress(address, emit),
        editAddress: (address) => _onEditAddress(address, emit),
        deleteAddress: (addressId) => _onDeleteAddress(addressId, emit),
        setDefaultAddress: (addressId) => _onSetDefaultAddress(addressId, emit),

        // Location detection events
        getCurrentLocation: () => _onGetCurrentLocation(emit),
        getAddressFromCoordinates: (latitude, longitude) => _onGetAddressFromCoordinates(latitude, longitude, emit),

        // Map and search events
        searchLocation: (query) => _onSearchLocation(query, emit),
        selectLocationOnMap: (latitude, longitude) => _onSelectLocationOnMap(latitude, longitude, emit),

        // Form management events
        updateFormField: (fieldName, value) => _onUpdateFormField(fieldName, value, emit),
        validateForm: () => _onValidateForm(emit),
        resetForm: () => _onResetForm(emit),
        setAddressType: (addressType) => _onSetAddressType(addressType, emit),
        setDefaultFlag: (isDefault) => _onSetDefaultFlag(isDefault, emit),

        orElse: () => null,
      ),
    );
  }

  Future<void> _onStarted(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      final defaultAddress = await addressService.getDefaultAddress();
      if (defaultAddress != null) {
        emit(LocationState.loaded(defaultAddress));
      } else {
        add(const LocationEvent.refreshLocation());
      }
    } catch (e) {
      emit(LocationState.error('Failed to load default location.'));
    }
  }

  Future<void> _onRefreshLocation(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());

    try {
      final savedAddresses = await addressService.getAllAddresses();
      final currentPosition = await locationService.getCurrentPosition();

      if (currentPosition != null) {
        if (savedAddresses.isEmpty) {
          final placemarks = await locationService.getAddressFromCoordinates(
            currentPosition.latitude,
            currentPosition.longitude,
          );
          if (placemarks != null && placemarks.isNotEmpty) {
            final address =
                _createTempAddress(currentPosition, placemarks.first);
            emit(LocationState.loaded(address));
            return;
          }
        } else {
          AddressModel? closest;
          double minDist = double.infinity;

          for (final addr in savedAddresses) {
            final dist = Geolocator.distanceBetween(
              currentPosition.latitude,
              currentPosition.longitude,
              (addr.latitude ?? 0.0).toDouble(),
              (addr.longitude ?? 0.0).toDouble(),
            );
            if (dist < minDist) {
              minDist = dist;
              closest = addr;
            }
          }

          if (closest != null) {
            if (!(closest.isDefault ?? false)) {
              await addressService.setDefaultAddress(closest.id ?? '');
            }
            emit(LocationState.loaded(closest));
            return;
          }
        }
      }

      final fallback = await addressService.getDefaultAddress();
      if (fallback != null) {
        emit(LocationState.loaded(fallback));
      } else {
        emit(const LocationState.error('No address available.'));
      }
    } catch (e) {
      emit(LocationState.error('Error while refreshing location.'));
    }
  }

  AddressModel _createTempAddress(Position pos, Placemark pm) {
    final fullAddress = [
      pm.street,
      pm.subLocality,
      pm.locality,
      pm.administrativeArea,
      pm.postalCode
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    final addressLine1 = [
      pm.street,
      pm.subLocality,
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    return AddressModel(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      fullAddress: fullAddress,
      addressLine1: addressLine1,
      city: pm.locality ?? '',
      state: pm.administrativeArea ?? '',
      pincode: pm.postalCode ?? '',
      latitude: pos.latitude,
      longitude: pos.longitude,
      addressType: 'current',
      isDefault: true,
    );
  }

  // Address management event handlers
  Future<void> _onLoadAddresses(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      final addresses = await addressService.getAllAddresses();
      final defaultAddress = await addressService.getDefaultAddress();
      emit(LocationState.addressesLoaded(addresses, defaultAddress?.id));
    } catch (e) {
      emit(LocationState.error('Failed to load addresses: $e'));
    }
  }

  Future<void> _onAddAddress(AddressModel address, Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      await addressService.saveAddress(address);
      emit(LocationState.addressSaved(address));
      // Reload addresses to get updated list
      add(const LocationEvent.loadAddresses());
    } catch (e) {
      emit(LocationState.error('Failed to save address: $e'));
    }
  }

  Future<void> _onEditAddress(AddressModel address, Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      await addressService.saveAddress(address);
      emit(LocationState.addressSaved(address));
      // Reload addresses to get updated list
      add(const LocationEvent.loadAddresses());
    } catch (e) {
      emit(LocationState.error('Failed to update address: $e'));
    }
  }

  Future<void> _onDeleteAddress(String addressId, Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      await addressService.deleteAddress(addressId);
      emit(LocationState.addressDeleted(addressId));
      // Reload addresses to get updated list
      add(const LocationEvent.loadAddresses());
    } catch (e) {
      emit(LocationState.error('Failed to delete address: $e'));
    }
  }

  Future<void> _onSetDefaultAddress(String addressId, Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      await addressService.setDefaultAddress(addressId);
      emit(LocationState.defaultAddressSet(addressId));
      // Reload addresses to get updated list
      add(const LocationEvent.loadAddresses());
    } catch (e) {
      emit(LocationState.error('Failed to set default address: $e'));
    }
  }

  // Location detection event handlers
  Future<void> _onGetCurrentLocation(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      final position = await addressService.getCurrentPosition();
      if (position != null) {
        final placemarks = await addressService.getAddressFromCoordinates(
          position.latitude,
          position.longitude,
        );
        if (placemarks != null && placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          final address = _buildAddressString(placemark);
          emit(LocationState.locationDetected(
            position.latitude,
            position.longitude,
            address,
          ));
        } else {
          emit(LocationState.locationDetected(
            position.latitude,
            position.longitude,
            'Location detected',
          ));
        }
      } else {
        emit(const LocationState.error('Failed to get current location'));
      }
    } catch (e) {
      emit(LocationState.error('Failed to get current location: $e'));
    }
  }

  Future<void> _onGetAddressFromCoordinates(
    double latitude,
    double longitude,
    Emitter<LocationState> emit,
  ) async {
    emit(const LocationState.loading());
    try {
      final placemarks = await addressService.getAddressFromCoordinates(latitude, longitude);
      if (placemarks != null && placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        final address = _buildAddressString(placemark);
        emit(LocationState.locationDetected(latitude, longitude, address));
      } else {
        emit(LocationState.locationDetected(latitude, longitude, 'Address not found'));
      }
    } catch (e) {
      emit(LocationState.error('Failed to get address from coordinates: $e'));
    }
  }

  String _buildAddressString(Placemark placemark) {
    final parts = [
      placemark.street,
      placemark.subLocality,
      placemark.locality,
      placemark.administrativeArea,
      placemark.postalCode,
    ].where((part) => part != null && part.isNotEmpty);
    return parts.join(', ');
  }

  // Map and search event handlers
  Future<void> _onSearchLocation(String query, Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      final locations = await addressService.searchAddresses(query);
      if (locations != null && locations.isNotEmpty) {
        final results = <AddressModel>[];
        for (final location in locations) {
          final placemarks = await addressService.getPlacemarkFromLocation(location);
          if (placemarks != null && placemarks.isNotEmpty) {
            final placemark = placemarks.first;
            final address = AddressModel(
              id: 'search_${DateTime.now().millisecondsSinceEpoch}',
              fullAddress: _buildAddressString(placemark),
              addressLine1: placemark.street ?? '',
              city: placemark.locality ?? '',
              state: placemark.administrativeArea ?? '',
              pincode: placemark.postalCode ?? '',
              latitude: location.latitude,
              longitude: location.longitude,
              addressType: 'search',
              isDefault: false,
            );
            results.add(address);
          }
        }
        emit(LocationState.searchResults(results));
      } else {
        emit(const LocationState.searchResults([]));
      }
    } catch (e) {
      emit(LocationState.error('Failed to search location: $e'));
    }
  }

  Future<void> _onSelectLocationOnMap(
    double latitude,
    double longitude,
    Emitter<LocationState> emit,
  ) async {
    emit(const LocationState.loading());
    try {
      final placemarks = await addressService.getAddressFromCoordinates(latitude, longitude);
      String address = 'Selected location';
      if (placemarks != null && placemarks.isNotEmpty) {
        address = _buildAddressString(placemarks.first);
      }
      emit(LocationState.mapState(LocationMapData(
        latitude: latitude,
        longitude: longitude,
        address: address,
        isLoading: false,
      )));
    } catch (e) {
      emit(LocationState.error('Failed to select location: $e'));
    }
  }

  // Form management event handlers
  LocationFormData _currentFormData = const LocationFormData();

  Future<void> _onUpdateFormField(
    String fieldName,
    String value,
    Emitter<LocationState> emit,
  ) async {
    switch (fieldName) {
      case 'addressLine1':
        _currentFormData = _currentFormData.copyWith(addressLine1: value);
        break;
      case 'landmark':
        _currentFormData = _currentFormData.copyWith(landmark: value);
        break;
      case 'city':
        _currentFormData = _currentFormData.copyWith(city: value);
        break;
      case 'state':
        _currentFormData = _currentFormData.copyWith(state: value);
        break;
      case 'pincode':
        _currentFormData = _currentFormData.copyWith(pincode: value);
        break;
    }
    emit(LocationState.formState(_currentFormData));
  }

  Future<void> _onValidateForm(Emitter<LocationState> emit) async {
    final errors = <String, String>{};

    if (_currentFormData.addressLine1.isEmpty) {
      errors['addressLine1'] = 'Address line is required';
    }
    if (_currentFormData.city.isEmpty) {
      errors['city'] = 'City is required';
    }
    if (_currentFormData.state.isEmpty) {
      errors['state'] = 'State is required';
    }
    if (_currentFormData.pincode.isEmpty) {
      errors['pincode'] = 'Pincode is required';
    } else if (_currentFormData.pincode.length != 6) {
      errors['pincode'] = 'Pincode must be 6 digits';
    }
    if (_currentFormData.latitude == 0.0 && _currentFormData.longitude == 0.0) {
      errors['location'] = 'Please select location on map';
    }

    if (errors.isEmpty) {
      _currentFormData = _currentFormData.copyWith(isValid: true, fieldErrors: {});
      emit(LocationState.formState(_currentFormData));
    } else {
      _currentFormData = _currentFormData.copyWith(isValid: false, fieldErrors: errors);
      emit(LocationState.formError(errors));
    }
  }

  Future<void> _onResetForm(Emitter<LocationState> emit) async {
    _currentFormData = const LocationFormData();
    emit(LocationState.formState(_currentFormData));
  }

  Future<void> _onSetAddressType(String addressType, Emitter<LocationState> emit) async {
    _currentFormData = _currentFormData.copyWith(addressType: addressType);
    emit(LocationState.formState(_currentFormData));
  }

  Future<void> _onSetDefaultFlag(bool isDefault, Emitter<LocationState> emit) async {
    _currentFormData = _currentFormData.copyWith(isDefault: isDefault);
    emit(LocationState.formState(_currentFormData));
  }

}
