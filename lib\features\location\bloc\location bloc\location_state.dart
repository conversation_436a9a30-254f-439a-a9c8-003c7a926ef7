import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../data/models/adress_model.dart';

part 'location_state.freezed.dart';

@freezed
class LocationFormData with _$LocationFormData {
  const factory LocationFormData({
    @Default('') String addressLine1,
    @Default('') String landmark,
    @Default('') String city,
    @Default('') String state,
    @Default('') String pincode,
    @Default('home') String addressType,
    @Default(false) bool isDefault,
    @Default(0.0) double latitude,
    @Default(0.0) double longitude,
    @Default({}) Map<String, String> fieldErrors,
    @Default(false) bool isValid,
  }) = _LocationFormData;
}

@freezed
class LocationMapData with _$LocationMapData {
  const factory LocationMapData({
    @Default(0.0) double latitude,
    @Default(0.0) double longitude,
    @Default('') String address,
    @Default(false) bool isLoading,
  }) = _LocationMapData;
}

@freezed
class LocationState with _$LocationState {
  // Core states
  const factory LocationState.initial() = _Initial;
  const factory LocationState.loading() = _Loading;
  const factory LocationState.loaded(AddressModel address) = _Loaded;
  const factory LocationState.error(String message) = _Error;

  // Address management states
  const factory LocationState.addressesLoaded(
          List<AddressModel> addresses, String? defaultAddressId) =
      _AddressesLoaded;
  const factory LocationState.addressSaved(AddressModel address) =
      _AddressSaved;
  const factory LocationState.addressDeleted(String addressId) =
      _AddressDeleted;
  const factory LocationState.defaultAddressSet(String addressId) =
      _DefaultAddressSet;

  // Form states
  const factory LocationState.formState(LocationFormData formData) = _FormState;
  const factory LocationState.formError(Map<String, String> errors) =
      _FormError;

  // Map and location states - Enhanced for better separation
  const factory LocationState.currentLocationLoading() =
      _CurrentLocationLoading;
  const factory LocationState.currentLocationDetected(
          double latitude, double longitude, String address) =
      _CurrentLocationDetected;
  const factory LocationState.reverseGeocodingLoading() =
      _ReverseGeocodingLoading;
  const factory LocationState.reverseGeocodingCompleted(
          double latitude, double longitude, String address) =
      _ReverseGeocodingCompleted;
  const factory LocationState.searchLoading() = _SearchLoading;
  const factory LocationState.searchResults(List<AddressModel> results) =
      _SearchResults;
  const factory LocationState.searchResultSelected(
      AddressModel selectedAddress) = _SearchResultSelected;
  const factory LocationState.mapState(LocationMapData mapData) = _MapState;
}
